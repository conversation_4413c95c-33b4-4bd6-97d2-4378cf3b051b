# YOLO可视化训练界面

基于PySide6和ultralytics库实现的YOLO模型训练图形用户界面，提供完整的模型训练、配置管理和监控功能。

## 功能特性

### 🚀 核心功能
- **可视化训练界面**: 直观的图形界面，无需命令行操作
- **预训练模型管理**: 自动下载和管理YOLO11系列预训练模型
- **全参数配置**: 支持ultralytics所有训练参数的自定义配置
- **实时训练监控**: 实时显示训练进度、损失曲线和性能指标
- **配置保存/加载**: 支持训练配置的保存和加载

### 📊 支持的模型
- **检测模型**: yolo11n.pt, yolo11s.pt, yolo11m.pt, yolo11l.pt, yolo11x.pt
- **分割模型**: yolo11n-seg.pt, yolo11s-seg.pt, yolo11m-seg.pt, yolo11l-seg.pt, yolo11x-seg.pt
- **姿态估计**: yolo11n-pose.pt, yolo11s-pose.pt, yolo11m-pose.pt, yolo11l-pose.pt, yolo11x-pose.pt
- **分类模型**: yolo11n-cls.pt, yolo11s-cls.pt, yolo11m-cls.pt, yolo11l-cls.pt, yolo11x-cls.pt
- **OBB模型**: yolo11n-obb.pt, yolo11s-obb.pt, yolo11m-obb.pt, yolo11l-obb.pt, yolo11x-obb.pt

### 🛠️ 配置选项
- **基础参数**: 训练轮数、批次大小、图像尺寸、设备选择等
- **优化器配置**: 学习率、动量、权重衰减、优化器类型等
- **数据增强**: HSV调整、几何变换、翻转、Mosaic、Mixup等
- **高级选项**: 混合精度训练、余弦学习率、早停等

## 安装要求

### 系统要求
- Python 3.8+
- Windows 10/11, macOS, 或 Linux

### 依赖包
所有依赖包已在`requirements.txt`中列出：

```bash
pip install -r requirements.txt
```

主要依赖：
- `ultralytics>=8.3.0` - YOLO模型训练库
- `PySide6>=6.6.0` - GUI框架
- `torch>=2.0.0` - PyTorch深度学习框架
- `torchvision>=0.15.0` - 计算机视觉库
- `opencv-python>=4.7.0` - 图像处理
- `numpy>=1.24.0` - 数值计算
- `pillow>=10.0.0` - 图像处理
- `pyyaml>=6.0` - YAML配置文件支持
- `requests>=2.32.0` - HTTP请求库

## 快速开始

### 1. 环境准备
```bash
# 克隆或下载项目文件
# 安装依赖
pip install -r requirements.txt
```

### 2. 启动应用
```bash
python run_yolo_trainer.py
```

### 3. 基本使用流程

#### 步骤1: 模型配置
1. 在"模型配置"标签页选择预训练模型
2. 点击"下载预训练模型"按钮自动下载模型
3. 选择任务类型（detect/segment/classify/pose/obb）

#### 步骤2: 数据集配置
1. 在"数据集配置"标签页设置数据集路径
2. 配置训练集、验证集、测试集路径
3. 设置类别数量和类别名称

#### 步骤3: 训练参数
1. 在"训练参数"标签页调整基础参数
2. 配置优化器参数和高级选项
3. 在"数据增强"标签页设置数据增强参数

#### 步骤4: 开始训练
1. 点击"开始训练"按钮
2. 在右侧监控面板查看训练进度
3. 训练完成后查看结果

## 界面说明

### 左侧配置面板
- **模型配置**: 选择和下载预训练模型
- **数据集配置**: 设置数据集路径和类别信息
- **训练参数**: 配置训练的各种参数
- **数据增强**: 设置数据增强选项

### 右侧监控面板
- **训练状态**: 显示当前训练状态和进度
- **训练日志**: 实时显示训练日志信息

### 控制按钮
- **开始训练**: 启动模型训练
- **停止训练**: 停止当前训练
- **保存配置**: 保存当前配置到文件
- **加载配置**: 从文件加载配置

## 数据集格式

支持YOLO格式的数据集，目录结构如下：

```
dataset/
├── train/
│   ├── images/
│   │   ├── img1.jpg
│   │   └── img2.jpg
│   └── labels/
│       ├── img1.txt
│       └── img2.txt
├── val/
│   ├── images/
│   └── labels/
└── test/
    ├── images/
    └── labels/
```

标签文件格式（每行一个对象）：
```
class_id center_x center_y width height
```

## 配置文件

### 训练配置
训练配置以JSON格式保存，包含所有训练参数：

```json
{
  "model": "models/yolo11n.pt",
  "task": "detect",
  "epochs": 100,
  "batch": 16,
  "imgsz": 640,
  "lr0": 0.01,
  "optimizer": "auto",
  ...
}
```

### 数据集配置
数据集配置以YAML格式保存：

```yaml
path: ./dataset
train: train/images
val: val/images
test: test/images
nc: 80
names: ['person', 'bicycle', 'car', ...]
```

## 模型下载

应用程序支持自动下载预训练模型，模型将保存在`models/`目录下。下载功能特性：

- **静默下载**: 通过后台调用浏览器或命令行工具下载
- **进度显示**: 实时显示下载进度
- **自动管理**: 下载完成后自动移动到项目目录
- **断点续传**: 支持下载中断后重新开始

## 训练监控

### 实时信息
- 当前训练轮次和总轮次
- 训练进度百分比
- 实时损失值和性能指标

### 日志功能
- 详细的训练日志记录
- 支持日志保存到文件
- 时间戳标记的消息

## 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 尝试手动下载模型文件到`models/`目录

2. **训练启动失败**
   - 检查数据集路径是否正确
   - 确认数据集格式符合YOLO要求
   - 检查GPU内存是否足够

3. **界面无法启动**
   - 确认已安装所有依赖包
   - 检查Python版本是否兼容

### 日志文件
应用程序会生成`yolo_trainer.log`日志文件，包含详细的运行信息和错误信息。

## 技术架构

### 主要组件
- `yolo_trainer_gui.py`: 主界面类
- `model_downloader.py`: 模型下载管理器
- `training_worker.py`: 训练工作线程
- `config_manager.py`: 配置管理器
- `utils.py`: 工具函数

### 设计特点
- **多线程设计**: 训练和下载在后台线程执行，不阻塞界面
- **信号槽机制**: 使用Qt信号槽实现组件间通信
- **模块化架构**: 各功能模块独立，便于维护和扩展

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持YOLO11系列模型训练
- 完整的GUI界面
- 自动模型下载功能
- 实时训练监控
