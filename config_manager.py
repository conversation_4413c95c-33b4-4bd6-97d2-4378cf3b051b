#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责管理训练配置和数据集配置
"""

import os
import yaml
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self.config_dir = Path("configs")
        self.config_dir.mkdir(exist_ok=True)
        
    def create_dataset_config(self, 
                            dataset_path: str,
                            train_path: str = "train",
                            val_path: str = "val", 
                            test_path: str = "test",
                            num_classes: int = 80,
                            class_names: List[str] = None) -> str:
        """创建数据集配置文件"""
        
        if not dataset_path:
            raise ValueError("数据集路径不能为空")
            
        # 处理类别名称
        if not class_names or len(class_names) == 0:
            class_names = [f"class_{i}" for i in range(num_classes)]
        elif len(class_names) != num_classes:
            # 调整类别名称数量
            if len(class_names) < num_classes:
                class_names.extend([f"class_{i}" for i in range(len(class_names), num_classes)])
            else:
                class_names = class_names[:num_classes]
                
        # 过滤空的类别名称
        class_names = [name.strip() for name in class_names if name.strip()]
        if len(class_names) != num_classes:
            class_names = [f"class_{i}" for i in range(num_classes)]
            
        # 构建路径
        dataset_path = Path(dataset_path)
        
        # 如果路径是相对路径，转换为绝对路径
        if not dataset_path.is_absolute():
            dataset_path = dataset_path.resolve()
            
        train_full_path = dataset_path / train_path
        val_full_path = dataset_path / val_path
        test_full_path = dataset_path / test_path
        
        # 创建配置字典
        config = {
            'path': str(dataset_path),
            'train': str(train_full_path) if train_full_path.exists() else train_path,
            'val': str(val_full_path) if val_full_path.exists() else val_path,
            'test': str(test_full_path) if test_full_path.exists() else test_path,
            'nc': num_classes,
            'names': class_names
        }
        
        # 保存配置文件
        config_file = self.config_dir / "dataset.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
        logger.info(f"数据集配置已保存到: {config_file}")
        return str(config_file)
        
    def load_dataset_config(self, config_path: str) -> Dict[str, Any]:
        """加载数据集配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            return config
        except Exception as e:
            logger.error(f"加载数据集配置失败: {e}")
            raise
            
    def save_training_config(self, config: Dict[str, Any], file_path: str = None) -> str:
        """保存训练配置"""
        if file_path is None:
            file_path = self.config_dir / "training_config.json"
        else:
            file_path = Path(file_path)
            
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            logger.info(f"训练配置已保存到: {file_path}")
            return str(file_path)
        except Exception as e:
            logger.error(f"保存训练配置失败: {e}")
            raise
            
    def load_training_config(self, file_path: str) -> Dict[str, Any]:
        """加载训练配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            return config
        except Exception as e:
            logger.error(f"加载训练配置失败: {e}")
            raise
            
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 模型配置
            'model': 'yolo11n.pt',
            'task': 'detect',
            
            # 基础训练参数
            'epochs': 100,
            'batch': 16,
            'imgsz': 640,
            'device': 'auto',
            'workers': 8,
            
            # 优化器参数
            'optimizer': 'auto',
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            
            # 高级参数
            'warmup_epochs': 3.0,
            'patience': 100,
            'save_period': -1,
            'amp': True,
            'cos_lr': False,
            
            # 数据增强参数
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 0.0,
            'translate': 0.1,
            'scale': 0.5,
            'shear': 0.0,
            'perspective': 0.0,
            'fliplr': 0.5,
            'flipud': 0.0,
            'mosaic': 1.0,
            'mixup': 0.0,
        }
        
    def validate_config(self, config: Dict[str, Any], require_data: bool = True) -> List[str]:
        """验证配置"""
        errors = []

        # 检查必需参数
        required_params = ['model', 'epochs', 'batch', 'imgsz']
        if require_data:
            required_params.append('data')

        for param in required_params:
            if param not in config or config[param] is None:
                errors.append(f"缺少必需参数: {param}")
                
        # 检查数值范围
        if 'epochs' in config and (config['epochs'] < 1 or config['epochs'] > 10000):
            errors.append("epochs必须在1-10000之间")
            
        if 'batch' in config and config['batch'] < -1:
            errors.append("batch必须大于等于-1")
            
        if 'imgsz' in config and (config['imgsz'] < 32 or config['imgsz'] > 2048):
            errors.append("imgsz必须在32-2048之间")
            
        if 'lr0' in config and (config['lr0'] <= 0 or config['lr0'] > 1):
            errors.append("lr0必须在0-1之间")
            
        # 检查文件路径
        if 'model' in config:
            model_path = config['model']
            if not model_path.endswith('.pt') and not model_path.endswith('.yaml'):
                errors.append("模型文件必须是.pt或.yaml格式")
                
        if 'data' in config:
            data_path = config['data']
            if data_path and not os.path.exists(data_path):
                errors.append(f"数据集配置文件不存在: {data_path}")
                
        return errors
        
    def create_coco_config(self) -> str:
        """创建COCO数据集配置"""
        coco_classes = [
            'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck', 'boat',
            'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench', 'bird', 'cat',
            'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra', 'giraffe', 'backpack',
            'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee', 'skis', 'snowboard', 'sports ball',
            'kite', 'baseball bat', 'baseball glove', 'skateboard', 'surfboard', 'tennis racket',
            'bottle', 'wine glass', 'cup', 'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple',
            'sandwich', 'orange', 'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake',
            'chair', 'couch', 'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop',
            'mouse', 'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
            'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
            'toothbrush'
        ]
        
        config = {
            'path': 'coco',
            'train': 'train2017.txt',
            'val': 'val2017.txt',
            'test': 'test-dev2017.txt',
            'nc': 80,
            'names': coco_classes
        }
        
        config_file = self.config_dir / "coco.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
        return str(config_file)
        
    def create_voc_config(self) -> str:
        """创建VOC数据集配置"""
        voc_classes = [
            'aeroplane', 'bicycle', 'bird', 'boat', 'bottle', 'bus', 'car', 'cat', 'chair',
            'cow', 'diningtable', 'dog', 'horse', 'motorbike', 'person', 'pottedplant',
            'sheep', 'sofa', 'train', 'tvmonitor'
        ]
        
        config = {
            'path': 'VOC',
            'train': 'train.txt',
            'val': 'val.txt',
            'test': 'test.txt',
            'nc': 20,
            'names': voc_classes
        }
        
        config_file = self.config_dir / "voc.yaml"
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            
        return str(config_file)
