#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试损失数据传递问题
"""

import sys
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QTextEdit, QPushButton, QHBoxLayout
from PySide6.QtCore import QTimer, Signal, QObject
from multi_plot_widget import MultiPlotWidget

class DebugSignals(QObject):
    """调试信号"""
    epoch_finished = Signal(int, dict)
    log_message = Signal(str)

class DebugWindow(QMainWindow):
    """调试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("损失数据传递调试")
        self.setGeometry(100, 100, 1400, 800)
        
        self.signals = DebugSignals()
        self.current_epoch = 0
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始调试")
        self.start_btn.clicked.connect(self.start_debug)
        
        self.stop_btn = QPushButton("停止调试")
        self.stop_btn.clicked.connect(self.stop_debug)
        self.stop_btn.setEnabled(False)
        
        self.test_loss_btn = QPushButton("测试损失数据")
        self.test_loss_btn.clicked.connect(self.test_loss_data)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.test_loss_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 分割布局
        content_layout = QHBoxLayout()
        
        # 左侧：日志
        log_widget = QWidget()
        log_layout = QVBoxLayout(log_widget)
        log_layout.addWidget(QLabel("调试日志:"))
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumWidth(400)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        content_layout.addWidget(log_widget)
        
        # 右侧：曲线图
        self.multi_plot = MultiPlotWidget()
        content_layout.addWidget(self.multi_plot)
        
        layout.addLayout(content_layout)
        
        # 连接信号
        self.signals.epoch_finished.connect(self.multi_plot.add_epoch_data)
        self.signals.log_message.connect(self.add_log)
        
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulate_epoch)
        
    def start_debug(self):
        """开始调试"""
        self.current_epoch = 0
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        self.log_text.clear()
        self.multi_plot.clear_all_data()
        
        self.add_log("开始调试损失数据传递...")
        self.timer.start(2000)  # 每2秒一个epoch
        
    def stop_debug(self):
        """停止调试"""
        self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.add_log("调试停止")
        
    def test_loss_data(self):
        """测试损失数据"""
        self.add_log("=== 测试损失数据 ===")
        
        # 模拟YOLO训练的损失数据
        test_metrics = {
            # 训练损失
            'train_loss': 9.828,
            'box_loss': 1.376,
            'seg_loss': 2.616,
            'cls_loss': 4.629,
            'dfl_loss': 1.207,
            
            # 验证损失
            'val_loss': 5.446,
            'val_box_loss': 0.856,
            'val_seg_loss': 1.234,
            'val_cls_loss': 2.567,
            'val_dfl_loss': 0.789,
            
            # mAP指标
            'box_mAP50': 0.0526,
            'seg_mAP50': 0.0396,
        }
        
        self.add_log(f"发送测试数据: {list(test_metrics.keys())}")
        
        # 发送数据
        self.signals.epoch_finished.emit(999, test_metrics)
        
        # 检查损失函数标签页
        self.check_loss_tab(test_metrics)
        
    def simulate_epoch(self):
        """模拟一个epoch"""
        if self.current_epoch >= 10:
            self.stop_debug()
            return
            
        self.current_epoch += 1
        
        # 生成模拟数据
        import random
        
        # 模拟损失递减
        base_factor = 1.0 - (self.current_epoch - 1) * 0.1
        noise = random.uniform(-0.1, 0.1)
        
        metrics = {
            # 训练损失
            'box_loss': max(0.1, (1.376 * base_factor) + noise),
            'seg_loss': max(0.1, (2.616 * base_factor) + noise),
            'cls_loss': max(0.1, (4.629 * base_factor) + noise),
            'dfl_loss': max(0.1, (1.207 * base_factor) + noise),
        }
        
        # 计算总损失
        metrics['train_loss'] = sum(metrics.values())
        
        # 验证损失（稍低一些）
        val_factor = base_factor * 0.8
        metrics.update({
            'val_box_loss': max(0.1, (0.856 * val_factor) + noise * 0.5),
            'val_seg_loss': max(0.1, (1.234 * val_factor) + noise * 0.5),
            'val_cls_loss': max(0.1, (2.567 * val_factor) + noise * 0.5),
            'val_dfl_loss': max(0.1, (0.789 * val_factor) + noise * 0.5),
        })
        
        metrics['val_loss'] = (metrics['val_box_loss'] + metrics['val_seg_loss'] + 
                              metrics['val_cls_loss'] + metrics['val_dfl_loss'])
        
        # mAP指标（逐渐提高）
        map_factor = self.current_epoch * 0.05
        metrics.update({
            'box_mAP50': min(0.9, 0.05 + map_factor + random.uniform(-0.02, 0.02)),
            'seg_mAP50': min(0.85, 0.04 + map_factor * 0.9 + random.uniform(-0.02, 0.02)),
            'box_precision': min(0.95, 0.1 + map_factor * 1.2 + random.uniform(-0.03, 0.03)),
            'box_recall': min(0.9, 0.15 + map_factor * 1.1 + random.uniform(-0.03, 0.03)),
        })
        
        self.add_log(f"Epoch {self.current_epoch}:")
        
        # 记录损失数据
        loss_data = {k: v for k, v in metrics.items() if 'loss' in k}
        loss_str = ", ".join([f"{k}: {v:.3f}" for k, v in loss_data.items()])
        self.add_log(f"  损失: {loss_str}")
        
        # 发送数据
        self.signals.epoch_finished.emit(self.current_epoch, metrics)
        
        # 检查数据是否正确传递
        self.check_loss_tab(metrics)
        
    def check_loss_tab(self, expected_metrics):
        """检查损失函数标签页的数据"""
        loss_tab = None
        for tab_name, plot_widget in self.multi_plot.plot_widgets.items():
            if tab_name == "损失函数":
                loss_tab = plot_widget
                break
        
        if loss_tab:
            stored_metrics = list(loss_tab.metrics_data.keys())
            expected_loss_metrics = [k for k in expected_metrics.keys() if 'loss' in k]
            
            missing = [m for m in expected_loss_metrics if m not in stored_metrics]
            if missing:
                self.add_log(f"  ❌ 缺失损失指标: {missing}")
            else:
                self.add_log(f"  ✓ 损失指标已存储: {len(stored_metrics)}个")
                
            # 检查最新数据
            if loss_tab.epochs:
                latest_epoch = max(loss_tab.epochs)
                latest_index = loss_tab.epochs.index(latest_epoch)
                
                for metric in stored_metrics:
                    if metric in loss_tab.metrics_data and len(loss_tab.metrics_data[metric]) > latest_index:
                        value = loss_tab.metrics_data[metric][latest_index]
                        if value is not None:
                            self.add_log(f"    {metric}: {value:.3f}")
        else:
            self.add_log("  ❌ 未找到损失函数标签页")
    
    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

def main():
    """主函数"""
    print("损失数据传递调试工具")
    print("=" * 50)
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    window = DebugWindow()
    window.show()
    
    print("调试窗口已启动！")
    print("功能说明:")
    print("1. 点击'开始调试'模拟训练过程")
    print("2. 点击'测试损失数据'发送测试数据")
    print("3. 观察左侧日志和右侧曲线图")
    print("4. 检查损失函数标签页是否正确显示数据")
    
    return app.exec()

if __name__ == "__main__":
    # 需要导入QLabel和QFont
    from PySide6.QtWidgets import QLabel
    from PySide6.QtGui import QFont
    
    sys.exit(main())
