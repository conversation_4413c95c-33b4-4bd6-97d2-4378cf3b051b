#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动识别功能演示
展示模型分析和数据集自动检测功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget, QPushButton, QTextEdit, QLabel, QLineEdit, QMessageBox
from PySide6.QtCore import Qt
from utils import get_model_info, auto_detect_dataset, setup_logging

class AutoDetectionDemo(QMainWindow):
    """自动识别功能演示窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YOLO自动识别功能演示")
        self.setGeometry(100, 100, 800, 600)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("YOLO自动识别功能演示")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("QLabel { font-size: 18px; font-weight: bold; margin: 10px; }")
        layout.addWidget(title_label)
        
        # 模型分析部分
        model_layout = QHBoxLayout()
        
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setPlaceholderText("输入模型路径，如: models/yolo11n.pt")
        self.model_path_edit.setText("models/yolo11n.pt")
        
        self.analyze_model_btn = QPushButton("分析模型")
        self.analyze_model_btn.clicked.connect(self.analyze_model)
        
        model_layout.addWidget(QLabel("模型路径:"))
        model_layout.addWidget(self.model_path_edit)
        model_layout.addWidget(self.analyze_model_btn)
        
        layout.addLayout(model_layout)
        
        # 数据集检测部分
        dataset_layout = QHBoxLayout()
        
        self.dataset_path_edit = QLineEdit()
        self.dataset_path_edit.setPlaceholderText("输入数据集路径")
        
        self.detect_dataset_btn = QPushButton("检测数据集")
        self.detect_dataset_btn.clicked.connect(self.detect_dataset)
        
        self.create_demo_btn = QPushButton("创建演示数据集")
        self.create_demo_btn.clicked.connect(self.create_demo_dataset)
        
        dataset_layout.addWidget(QLabel("数据集路径:"))
        dataset_layout.addWidget(self.dataset_path_edit)
        dataset_layout.addWidget(self.detect_dataset_btn)
        dataset_layout.addWidget(self.create_demo_btn)
        
        layout.addLayout(dataset_layout)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        layout.addWidget(self.result_text)
        
        # 清空按钮
        clear_btn = QPushButton("清空结果")
        clear_btn.clicked.connect(self.clear_results)
        layout.addWidget(clear_btn)
        
        self.log_message("=== YOLO自动识别功能演示 ===")
        self.log_message("1. 输入模型路径，点击'分析模型'")
        self.log_message("2. 输入数据集路径，点击'检测数据集'")
        self.log_message("3. 或点击'创建演示数据集'创建测试数据")
        self.log_message("")
        
    def log_message(self, message: str):
        """添加日志消息"""
        self.result_text.append(message)
        
    def analyze_model(self):
        """分析模型"""
        model_path = self.model_path_edit.text().strip()
        
        if not model_path:
            QMessageBox.warning(self, "警告", "请输入模型路径")
            return
            
        if not os.path.exists(model_path):
            self.log_message(f"❌ 模型文件不存在: {model_path}")
            return
            
        try:
            self.log_message(f"🔍 正在分析模型: {model_path}")
            
            # 分析模型
            model_info = get_model_info(model_path)
            
            self.log_message("📊 模型分析结果:")
            self.log_message(f"  ✓ 文件存在: {model_info.get('exists')}")
            self.log_message(f"  📁 文件大小: {model_info.get('size_formatted')}")
            self.log_message(f"  🏗️ 模型类型: {model_info.get('type')}")
            self.log_message(f"  🎯 任务类型: {model_info.get('task')}")
            self.log_message(f"  📊 类别数量: {model_info.get('num_classes')}")
            self.log_message(f"  🔧 架构类型: {model_info.get('architecture')}")
            self.log_message("")
            
        except Exception as e:
            self.log_message(f"❌ 模型分析失败: {str(e)}")
            
    def detect_dataset(self):
        """检测数据集"""
        dataset_path = self.dataset_path_edit.text().strip()
        
        if not dataset_path:
            QMessageBox.warning(self, "警告", "请输入数据集路径")
            return
            
        if not os.path.exists(dataset_path):
            self.log_message(f"❌ 数据集路径不存在: {dataset_path}")
            return
            
        try:
            self.log_message(f"🔍 正在检测数据集: {dataset_path}")
            
            # 检测数据集
            dataset_info = auto_detect_dataset(dataset_path)
            
            self.log_message("📊 数据集检测结果:")
            self.log_message(f"  ✓ 结构有效: {dataset_info.get('valid')}")
            self.log_message(f"  🎯 任务类型: {dataset_info.get('task')}")
            self.log_message(f"  📂 包含分割: {', '.join(dataset_info.get('splits', []))}")
            self.log_message(f"  🖼️ 总图像数: {dataset_info.get('total_images')}")
            self.log_message(f"  🏷️ 总标签数: {dataset_info.get('total_labels')}")
            self.log_message(f"  📊 类别数量: {dataset_info.get('num_classes')}")
            self.log_message(f"  📋 类别来源: {dataset_info.get('class_source')}")
            
            if dataset_info.get('class_names'):
                class_names = dataset_info['class_names']
                if len(class_names) <= 10:
                    self.log_message(f"  🏷️ 类别名称: {', '.join(class_names)}")
                else:
                    self.log_message(f"  🏷️ 类别名称: {', '.join(class_names[:10])}... (共{len(class_names)}个)")
                    
            if dataset_info.get('warnings'):
                self.log_message("  ⚠️ 警告:")
                for warning in dataset_info['warnings']:
                    self.log_message(f"    • {warning}")
                    
            if dataset_info.get('errors'):
                self.log_message("  ❌ 错误:")
                for error in dataset_info['errors']:
                    self.log_message(f"    • {error}")
                    
            self.log_message("")
            
        except Exception as e:
            self.log_message(f"❌ 数据集检测失败: {str(e)}")
            
    def create_demo_dataset(self):
        """创建演示数据集"""
        demo_path = Path("demo_dataset")
        
        try:
            self.log_message("🔨 正在创建演示数据集...")
            
            # 创建目录结构
            for split in ['train', 'val', 'test']:
                images_dir = demo_path / split / 'images'
                labels_dir = demo_path / split / 'labels'
                images_dir.mkdir(parents=True, exist_ok=True)
                labels_dir.mkdir(parents=True, exist_ok=True)
                
                # 创建示例文件
                num_files = 5 if split == 'train' else 2
                for i in range(num_files):
                    # 创建空的图像文件
                    image_file = images_dir / f"demo_{i:03d}.jpg"
                    image_file.touch()
                    
                    # 创建示例标签文件
                    label_file = labels_dir / f"demo_{i:03d}.txt"
                    with open(label_file, 'w') as f:
                        # 不同的标签格式示例
                        if split == 'train' and i == 0:
                            # 检测格式
                            f.write("0 0.5 0.5 0.3 0.4\n")
                            f.write("1 0.2 0.3 0.1 0.2\n")
                        elif split == 'train' and i == 1:
                            # 分割格式（简化）
                            f.write("0 0.5 0.5 0.3 0.4 0.1 0.1 0.9 0.1 0.9 0.9 0.1 0.9\n")
                        else:
                            # 标准检测格式
                            f.write(f"{i % 3} 0.{5+i} 0.{4+i} 0.{2+i} 0.{3+i}\n")
            
            # 创建类别文件
            classes_file = demo_path / 'classes.txt'
            with open(classes_file, 'w', encoding='utf-8') as f:
                f.write("person\n")
                f.write("car\n")
                f.write("bicycle\n")
                
            # 创建YAML配置文件
            yaml_file = demo_path / 'data.yaml'
            with open(yaml_file, 'w', encoding='utf-8') as f:
                f.write(f"path: {demo_path.absolute()}\n")
                f.write("train: train/images\n")
                f.write("val: val/images\n")
                f.write("test: test/images\n")
                f.write("nc: 3\n")
                f.write("names:\n")
                f.write("  - person\n")
                f.write("  - car\n")
                f.write("  - bicycle\n")
                
            self.log_message(f"✓ 演示数据集创建完成: {demo_path.absolute()}")
            self.dataset_path_edit.setText(str(demo_path.absolute()))
            self.log_message("💡 现在可以点击'检测数据集'来测试自动检测功能")
            self.log_message("")
            
        except Exception as e:
            self.log_message(f"❌ 创建演示数据集失败: {str(e)}")
            
    def clear_results(self):
        """清空结果"""
        self.result_text.clear()
        self.log_message("=== 结果已清空 ===")
        self.log_message("")

def main():
    """主函数"""
    setup_logging()
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    window = AutoDetectionDemo()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
