#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查YOLO回调函数和损失数据结构
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def inspect_yolo_structure():
    """检查YOLO的结构"""
    print("检查YOLO回调函数和损失数据结构")
    print("=" * 60)
    
    try:
        from ultralytics import YOLO
        from ultralytics.utils.callbacks import default_callbacks
        
        print("1. YOLO默认回调函数:")
        for callback_name in default_callbacks.keys():
            print(f"  - {callback_name}")
        
        print("\n2. 创建YOLO模型并检查trainer结构:")
        
        # 创建一个小模型用于检查
        model = YOLO('yolo11n.pt')
        
        print("  模型创建成功")
        
        # 检查trainer的属性
        if hasattr(model, 'trainer'):
            trainer = model.trainer
            print(f"  trainer类型: {type(trainer)}")
            
            # 检查trainer的属性
            trainer_attrs = [attr for attr in dir(trainer) if not attr.startswith('_')]
            loss_related = [attr for attr in trainer_attrs if 'loss' in attr.lower()]
            
            print(f"  trainer损失相关属性: {loss_related}")
        
        print("\n3. 检查训练过程中的数据结构:")
        
        # 创建一个自定义回调来检查数据
        def inspect_callback(trainer):
            print(f"    Epoch: {getattr(trainer, 'epoch', 'N/A')}")
            
            # 检查损失相关属性
            loss_attrs = ['loss_items', 'tloss', 'loss', 'losses']
            for attr in loss_attrs:
                if hasattr(trainer, attr):
                    value = getattr(trainer, attr)
                    print(f"    {attr}: {type(value)} = {value}")
            
            # 检查metrics
            if hasattr(trainer, 'metrics'):
                metrics = trainer.metrics
                print(f"    metrics类型: {type(metrics)}")
                if hasattr(metrics, 'results_dict'):
                    print(f"    metrics.results_dict: {metrics.results_dict}")
        
        # 添加自定义回调
        model.add_callback('on_train_epoch_end', inspect_callback)
        
        print("  自定义回调已添加")
        
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

def create_minimal_test():
    """创建最小化的训练测试"""
    print("\n4. 创建最小化训练测试:")
    
    try:
        from ultralytics import YOLO
        import tempfile
        import os
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"  临时目录: {temp_dir}")
            
            # 创建最小的数据集配置
            dataset_yaml = f"""
path: {temp_dir}
train: images
val: images
nc: 1
names: ['object']
"""
            
            # 创建目录结构
            images_dir = os.path.join(temp_dir, 'images')
            labels_dir = os.path.join(temp_dir, 'labels')
            os.makedirs(images_dir, exist_ok=True)
            os.makedirs(labels_dir, exist_ok=True)
            
            # 创建一个虚拟图片和标签（用于测试）
            import numpy as np
            from PIL import Image
            
            # 创建640x640的虚拟图片
            img = Image.fromarray(np.random.randint(0, 255, (640, 640, 3), dtype=np.uint8))
            img_path = os.path.join(images_dir, 'test.jpg')
            img.save(img_path)
            
            # 创建对应的标签文件
            label_path = os.path.join(labels_dir, 'test.txt')
            with open(label_path, 'w') as f:
                f.write('0 0.5 0.5 0.2 0.2\n')  # 类别0，中心位置，宽高
            
            # 保存数据集配置
            dataset_path = os.path.join(temp_dir, 'dataset.yaml')
            with open(dataset_path, 'w') as f:
                f.write(dataset_yaml)
            
            print("  虚拟数据集创建完成")
            
            # 创建模型
            model = YOLO('yolo11n.pt')
            
            # 添加详细的回调函数
            def detailed_callback(trainer):
                print(f"\n=== Epoch {getattr(trainer, 'epoch', 'N/A')} 回调 ===")
                
                # 检查所有可能的损失属性
                for attr_name in dir(trainer):
                    if 'loss' in attr_name.lower() and not attr_name.startswith('_'):
                        try:
                            value = getattr(trainer, attr_name)
                            if value is not None:
                                print(f"  {attr_name}: {type(value)} = {value}")
                        except:
                            pass
                
                # 特别检查loss_items
                if hasattr(trainer, 'loss_items'):
                    loss_items = trainer.loss_items
                    print(f"  loss_items详细信息:")
                    print(f"    类型: {type(loss_items)}")
                    print(f"    值: {loss_items}")
                    
                    if hasattr(loss_items, 'shape'):
                        print(f"    形状: {loss_items.shape}")
                    if hasattr(loss_items, 'cpu'):
                        print(f"    CPU值: {loss_items.cpu()}")
                    if hasattr(loss_items, 'numpy'):
                        print(f"    numpy值: {loss_items.cpu().numpy()}")
            
            model.add_callback('on_train_epoch_end', detailed_callback)
            
            print("  开始最小化训练（1个epoch）...")
            
            # 训练1个epoch
            results = model.train(
                data=dataset_path,
                epochs=1,
                batch=1,
                imgsz=640,
                verbose=True,
                device='cpu'  # 使用CPU避免GPU问题
            )
            
            print("  训练完成")
            
    except Exception as e:
        print(f"最小化测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("YOLO回调函数和损失数据结构检查")
    print("=" * 60)
    
    # 检查YOLO结构
    inspect_yolo_structure()
    
    # 创建最小化测试
    create_minimal_test()
    
    print("\n" + "=" * 60)
    print("检查完成")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
