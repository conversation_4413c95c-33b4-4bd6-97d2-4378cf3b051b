@echo off
echo YOLO可视化训练界面安装器
echo ========================

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    echo 请先安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo.
echo 升级pip...
python -m pip install --upgrade pip

echo.
echo 安装依赖包...
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo 错误: 依赖包安装失败
    pause
    exit /b 1
)

echo.
echo 运行测试...
python test_gui.py

if %errorlevel% neq 0 (
    echo 错误: 测试失败
    pause
    exit /b 1
)

echo.
echo ========================
echo 安装完成！
echo 您现在可以运行 start.bat 启动应用程序
echo 或者直接运行: python run_yolo_trainer.py
echo ========================

pause
