#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型下载器
负责从官网下载YOLO预训练模型
"""

import os
import requests
import logging
from pathlib import Path
from typing import Dict, Optional
from urllib.parse import urlparse

from PySide6.QtCore import QObject, QThread, Signal
import webbrowser
import subprocess
import tempfile
import shutil

logger = logging.getLogger(__name__)


class ModelDownloader(QObject):
    """模型下载器"""
    
    # 信号定义
    progress_updated = Signal(int)  # 下载进度更新
    download_finished = Signal(str)  # 下载完成，参数为模型路径
    download_error = Signal(str)    # 下载错误，参数为错误信息
    
    # YOLO11模型下载URL映射
    MODEL_URLS = {
        "yolo11n.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt",
        "yolo11s.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s.pt",
        "yolo11m.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m.pt",
        "yolo11l.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l.pt",
        "yolo11x.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x.pt",
        
        # 分割模型
        "yolo11n-seg.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n-seg.pt",
        "yolo11s-seg.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s-seg.pt",
        "yolo11m-seg.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m-seg.pt",
        "yolo11l-seg.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l-seg.pt",
        "yolo11x-seg.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x-seg.pt",
        
        # 姿态估计模型
        "yolo11n-pose.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n-pose.pt",
        "yolo11s-pose.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s-pose.pt",
        "yolo11m-pose.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m-pose.pt",
        "yolo11l-pose.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l-pose.pt",
        "yolo11x-pose.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x-pose.pt",
        
        # 分类模型
        "yolo11n-cls.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n-cls.pt",
        "yolo11s-cls.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s-cls.pt",
        "yolo11m-cls.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m-cls.pt",
        "yolo11l-cls.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l-cls.pt",
        "yolo11x-cls.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x-cls.pt",
        
        # OBB模型
        "yolo11n-obb.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n-obb.pt",
        "yolo11s-obb.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11s-obb.pt",
        "yolo11m-obb.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11m-obb.pt",
        "yolo11l-obb.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11l-obb.pt",
        "yolo11x-obb.pt": "https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11x-obb.pt",
    }
    
    def __init__(self):
        super().__init__()
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        self.download_thread = None
        
    def download_model(self, model_name: str):
        """下载模型"""
        if model_name not in self.MODEL_URLS:
            self.download_error.emit(f"未知的模型: {model_name}")
            return
            
        # 检查模型是否已存在
        model_path = self.models_dir / model_name
        if model_path.exists():
            self.download_finished.emit(str(model_path))
            return
            
        # 创建下载线程
        self.download_thread = DownloadThread(
            self.MODEL_URLS[model_name], 
            str(model_path)
        )
        
        # 连接信号
        self.download_thread.progress_updated.connect(self.progress_updated)
        self.download_thread.download_finished.connect(self.download_finished)
        self.download_thread.download_error.connect(self.download_error)
        
        # 开始下载
        self.download_thread.start()
        
    def get_model_path(self, model_name: str) -> Optional[str]:
        """获取模型路径"""
        model_path = self.models_dir / model_name
        if model_path.exists():
            return str(model_path)
        return None
        
    def is_model_downloaded(self, model_name: str) -> bool:
        """检查模型是否已下载"""
        model_path = self.models_dir / model_name
        return model_path.exists()


class DownloadThread(QThread):
    """下载线程"""
    
    progress_updated = Signal(int)
    download_finished = Signal(str)
    download_error = Signal(str)
    
    def __init__(self, url: str, save_path: str):
        super().__init__()
        self.url = url
        self.save_path = save_path
        self.is_cancelled = False
        
    def run(self):
        """执行下载"""
        try:
            logger.info(f"开始下载模型: {self.url} -> {self.save_path}")
            
            # 首先尝试使用浏览器静默下载
            if self.download_with_browser():
                return
                
            # 如果浏览器下载失败，使用requests下载
            self.download_with_requests()
            
        except Exception as e:
            logger.error(f"下载失败: {e}")
            self.download_error.emit(str(e))
            
    def download_with_browser(self) -> bool:
        """使用浏览器静默下载"""
        try:
            # 在Windows上优先使用PowerShell下载
            if os.name == 'nt':  # Windows
                if self.download_with_powershell():
                    return True

            # 创建临时目录
            temp_dir = tempfile.mkdtemp()
            temp_file = os.path.join(temp_dir, os.path.basename(self.save_path))

            # 使用wget或curl下载（如果可用）
            if self.download_with_curl(temp_file):
                # 移动文件到目标位置
                shutil.move(temp_file, self.save_path)
                shutil.rmtree(temp_dir)
                self.download_finished.emit(self.save_path)
                return True

            # 清理临时目录
            shutil.rmtree(temp_dir)
            return False

        except Exception as e:
            logger.warning(f"浏览器下载失败: {e}")
            return False

    def download_with_powershell(self) -> bool:
        """使用PowerShell下载（Windows）"""
        try:
            # PowerShell下载命令
            ps_command = f'''
            $ProgressPreference = 'SilentlyContinue'
            Invoke-WebRequest -Uri "{self.url}" -OutFile "{self.save_path}" -UseBasicParsing
            '''

            result = subprocess.run([
                "powershell", "-Command", ps_command
            ], capture_output=True, text=True, timeout=600)

            if result.returncode == 0 and os.path.exists(self.save_path):
                logger.info("PowerShell下载成功")
                self.download_finished.emit(self.save_path)
                return True
            else:
                logger.warning(f"PowerShell下载失败: {result.stderr}")
                return False

        except (subprocess.TimeoutExpired, subprocess.SubprocessError) as e:
            logger.warning(f"PowerShell下载失败: {e}")
            return False

    def download_with_wget(self, save_path: str) -> bool:
        """使用wget下载"""
        try:
            result = subprocess.run([
                "wget", "-O", save_path, self.url, "--progress=dot"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("wget下载成功")
                return True
            else:
                logger.warning(f"wget下载失败: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            logger.warning(f"wget不可用: {e}")
            return False
            
    def download_with_curl(self, save_path: str) -> bool:
        """使用curl下载"""
        try:
            result = subprocess.run([
                "curl", "-L", "-o", save_path, self.url, "--progress-bar"
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                logger.info("curl下载成功")
                return True
            else:
                logger.warning(f"curl下载失败: {result.stderr}")
                return False
                
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            logger.warning(f"curl不可用: {e}")
            return False
            
    def download_with_requests(self):
        """使用requests下载"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            logger.info(f"开始使用requests下载: {self.url}")
            response = requests.get(self.url, headers=headers, stream=True, timeout=30)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0

            logger.info(f"文件大小: {total_size} 字节")

            # 确保目录存在
            os.makedirs(os.path.dirname(self.save_path), exist_ok=True)

            with open(self.save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if self.is_cancelled:
                        logger.info("下载被取消")
                        return

                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)

                        if total_size > 0:
                            progress = int((downloaded_size / total_size) * 100)
                            self.progress_updated.emit(progress)

                        # 每下载1MB输出一次日志
                        if downloaded_size % (1024 * 1024) == 0:
                            mb_downloaded = downloaded_size / (1024 * 1024)
                            logger.info(f"已下载: {mb_downloaded:.1f} MB")

            logger.info(f"requests下载完成: {self.save_path}")
            self.download_finished.emit(self.save_path)

        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {e}")
            self.download_error.emit(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"requests下载失败: {e}")
            self.download_error.emit(str(e))
            
    def cancel(self):
        """取消下载"""
        self.is_cancelled = True
