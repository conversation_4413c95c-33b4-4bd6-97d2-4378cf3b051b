#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多标签页训练曲线图组件
支持按指标类型分组显示的训练曲线
"""

import logging
from typing import Dict, Any

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTabWidget,
                               QPushButton, QCheckBox, QLabel, QGroupBox)
from PySide6.QtCore import Qt

try:
    import matplotlib
    matplotlib.use('Qt5Agg')
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

logger = logging.getLogger(__name__)

class SinglePlotWidget(QWidget):
    """单个图表组件"""
    
    def __init__(self, title: str, metrics_config: Dict[str, str], parent=None):
        super().__init__(parent)
        self.title = title
        self.metrics_config = metrics_config  # {metric_name: color}
        self.epochs = []
        self.metrics_data = {}
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_group = QGroupBox("控制")
        control_layout = QHBoxLayout(control_group)
        
        # 自动缩放
        self.auto_scale_cb = QCheckBox("自动缩放")
        self.auto_scale_cb.setChecked(True)
        control_layout.addWidget(self.auto_scale_cb)
        
        # 显示网格
        self.show_grid_cb = QCheckBox("显示网格")
        self.show_grid_cb.setChecked(True)
        self.show_grid_cb.toggled.connect(self.update_plot)
        control_layout.addWidget(self.show_grid_cb)
        
        # 指标选择
        self.metric_checkboxes = {}
        for metric_name, color in self.metrics_config.items():
            cb = QCheckBox(metric_name)
            cb.setStyleSheet(f"QCheckBox {{ color: {color}; font-weight: bold; }}")
            cb.setChecked(True)  # 默认全部选中
            cb.toggled.connect(self.update_plot)
            self.metric_checkboxes[metric_name] = cb
            control_layout.addWidget(cb)
        
        control_layout.addStretch()
        layout.addWidget(control_group)
        
        # 图表区域
        if MATPLOTLIB_AVAILABLE:
            self.create_plot_area()
            layout.addWidget(self.canvas)
        else:
            no_plot_label = QLabel("matplotlib未安装，无法显示曲线图")
            no_plot_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(no_plot_label)
            
    def create_plot_area(self):
        """创建图表区域"""
        self.figure = Figure(figsize=(10, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.figure.patch.set_facecolor('white')
        
        # 设置中文字体
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
        except:
            pass
        
        self.ax = self.figure.add_subplot(111)
        self.ax.set_xlabel('Epoch', fontsize=12)
        self.ax.set_ylabel('Value', fontsize=12)
        self.ax.set_title(self.title, fontsize=14, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        
        # 初始化空图
        self.ax.text(0.5, 0.5, '等待训练数据...', 
                    transform=self.ax.transAxes, 
                    ha='center', va='center',
                    fontsize=14, alpha=0.5)
        
        self.canvas.draw()
        
    def add_epoch_data(self, epoch: int, metrics: Dict[str, Any]):
        """添加一个epoch的数据"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        # 添加epoch
        if epoch not in self.epochs:
            self.epochs.append(epoch)
            
        # 添加相关的指标数据
        for metric_name in self.metrics_config.keys():
            if metric_name in metrics:
                value = metrics[metric_name]
                if isinstance(value, (int, float)) and not np.isnan(value):
                    if metric_name not in self.metrics_data:
                        self.metrics_data[metric_name] = []
                    
                    # 确保数据长度一致
                    while len(self.metrics_data[metric_name]) < len(self.epochs) - 1:
                        self.metrics_data[metric_name].append(None)
                        
                    self.metrics_data[metric_name].append(value)
        
        # 更新图表
        self.update_plot()
        
    def update_plot(self):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE or not self.epochs:
            return
            
        self.ax.clear()
        
        # 设置基本属性
        self.ax.set_xlabel('Epoch', fontsize=12)
        self.ax.set_ylabel('Value', fontsize=12)
        self.ax.set_title(self.title, fontsize=14, fontweight='bold')
        
        # 显示网格
        if self.show_grid_cb.isChecked():
            self.ax.grid(True, alpha=0.3)
        
        # 绘制选中的指标
        plotted_any = False
        for metric_name, checkbox in self.metric_checkboxes.items():
            if checkbox.isChecked() and metric_name in self.metrics_data:
                values = self.metrics_data[metric_name]
                if values and any(v is not None for v in values):
                    # 过滤None值
                    epochs_filtered = []
                    values_filtered = []
                    for i, (epoch, value) in enumerate(zip(self.epochs, values)):
                        if value is not None:
                            epochs_filtered.append(epoch)
                            values_filtered.append(value)
                    
                    if epochs_filtered and values_filtered:
                        color = self.metrics_config.get(metric_name, '#000000')
                        self.ax.plot(epochs_filtered, values_filtered, 
                                   label=metric_name, color=color, 
                                   linewidth=2, marker='o', markersize=4)
                        plotted_any = True
        
        if plotted_any:
            # 添加图例
            self.ax.legend()
            
            # 自动缩放
            if self.auto_scale_cb.isChecked():
                self.ax.relim()
                self.ax.autoscale_view()
        else:
            # 没有数据时显示提示
            self.ax.text(0.5, 0.5, '等待训练数据...', 
                        transform=self.ax.transAxes, 
                        ha='center', va='center',
                        fontsize=14, alpha=0.5)
        
        # 调整布局
        self.figure.tight_layout()
        self.canvas.draw()
        
    def clear_data(self):
        """清空数据"""
        self.epochs.clear()
        self.metrics_data.clear()
        self.update_plot()

class MultiPlotWidget(QWidget):
    """多标签页训练曲线图组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 定义不同类型的指标和颜色
        self.plot_configs = {
            "mAP指标": {
                'box_mAP50': '#1f77b4',
                'box_mAP50-95': '#ff7f0e',
                'seg_mAP50': '#2ca02c',
                'seg_mAP50-95': '#d62728',
                'pose_mAP50': '#9467bd',
                'pose_mAP50-95': '#8c564b',
            },
            "精度召回": {
                'box_precision': '#e377c2',
                'box_recall': '#7f7f7f',
                'seg_precision': '#bcbd22',
                'seg_recall': '#17becf',
            },
            "损失函数": {
                'train_loss': '#ff0000',
                'val_loss': '#0000ff',
                'box_loss': '#ff9896',
                'seg_loss': '#98df8a',
                'cls_loss': '#ffbb78',
                'dfl_loss': '#c5b0d5',
            },
            "分类准确率": {
                'top1_acc': '#ff9896',
                'top5_acc': '#98df8a',
            }
        }
        
        self.plot_widgets = {}
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 全局控制按钮
        control_layout = QHBoxLayout()
        
        self.clear_all_btn = QPushButton("清空所有数据")
        self.clear_all_btn.clicked.connect(self.clear_all_data)
        control_layout.addWidget(self.clear_all_btn)
        
        self.save_all_btn = QPushButton("保存所有图表")
        self.save_all_btn.clicked.connect(self.save_all_plots)
        self.save_all_btn.setEnabled(MATPLOTLIB_AVAILABLE)
        control_layout.addWidget(self.save_all_btn)
        
        control_layout.addStretch()
        layout.addLayout(control_layout)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)
        
        # 为每种指标类型创建标签页
        for tab_name, metrics_config in self.plot_configs.items():
            plot_widget = SinglePlotWidget(tab_name, metrics_config)
            self.plot_widgets[tab_name] = plot_widget
            self.tab_widget.addTab(plot_widget, tab_name)
            
    def add_epoch_data(self, epoch: int, metrics: Dict[str, Any]):
        """添加一个epoch的数据到所有相关图表"""
        for plot_widget in self.plot_widgets.values():
            plot_widget.add_epoch_data(epoch, metrics)
            
    def clear_all_data(self):
        """清空所有数据"""
        for plot_widget in self.plot_widgets.values():
            plot_widget.clear_data()
        logger.info("所有训练曲线数据已清空")
        
    def save_all_plots(self):
        """保存所有图表"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        from PySide6.QtWidgets import QFileDialog
        
        directory = QFileDialog.getExistingDirectory(self, "选择保存目录")
        if directory:
            try:
                for tab_name, plot_widget in self.plot_widgets.items():
                    if hasattr(plot_widget, 'figure'):
                        filename = f"{directory}/training_{tab_name.replace(' ', '_')}.png"
                        plot_widget.figure.savefig(filename, dpi=300, bbox_inches='tight')
                        
                logger.info(f"所有训练曲线图已保存到: {directory}")
            except Exception as e:
                logger.error(f"保存图片失败: {e}")
                
    def get_current_metrics(self):
        """获取当前的指标数据"""
        all_data = {}
        for tab_name, plot_widget in self.plot_widgets.items():
            all_data[tab_name] = {
                'epochs': plot_widget.epochs.copy(),
                'metrics_data': {k: v.copy() for k, v in plot_widget.metrics_data.items()}
            }
        return all_data
