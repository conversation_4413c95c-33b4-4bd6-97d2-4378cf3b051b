#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO训练器启动脚本
"""

import sys
import logging
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils import setup_logging, check_dependencies, get_system_info, create_models_dir
from yolo_trainer_gui import YOLOTrainerGUI

from PySide6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QFont


def check_environment():
    """检查运行环境"""
    print("检查运行环境...")

    # 检查Python版本
    if sys.version_info < (3, 8):
        print(f"错误: Python版本过低 ({sys.version})")
        print("请使用Python 3.8或更高版本")
        return False

    # 检查依赖项
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"缺少以下依赖项: {', '.join(missing_deps)}")
        print("请使用以下命令安装:")
        print(f"pip install {' '.join(missing_deps)}")
        print("或运行: pip install -r requirements.txt")
        return False

    # 获取系统信息
    sys_info = get_system_info()
    print(f"✓ Python版本: {sys_info['python_version']}")
    print(f"✓ 操作系统: {sys_info['platform']}")
    print(f"✓ PyTorch版本: {sys_info.get('torch_version', 'Not installed')}")
    print(f"✓ CUDA可用: {sys_info.get('cuda_available', False)}")
    print(f"✓ Ultralytics版本: {sys_info.get('ultralytics_version', 'Not installed')}")

    if sys_info.get('cuda_available'):
        print(f"✓ CUDA设备数量: {sys_info.get('cuda_device_count', 0)}")
        for i, name in enumerate(sys_info.get('cuda_device_names', [])):
            print(f"  GPU {i}: {name}")
    else:
        print("⚠ 未检测到CUDA设备，将使用CPU训练")

    return True


def create_splash_screen(app):
    """创建启动画面"""
    # 创建简单的启动画面
    splash_pix = QPixmap(400, 300)
    splash_pix.fill(Qt.white)
    
    splash = QSplashScreen(splash_pix, Qt.WindowStaysOnTopHint)
    splash.setMask(splash_pix.mask())
    
    # 添加文本
    splash.showMessage(
        "YOLO可视化训练界面\n\n正在启动...",
        Qt.AlignCenter | Qt.AlignBottom,
        Qt.black
    )
    
    splash.show()
    app.processEvents()
    
    return splash


def main():
    """主函数"""
    # 设置日志
    setup_logging(level=logging.INFO, log_file="yolo_trainer.log")
    logger = logging.getLogger(__name__)
    
    logger.info("启动YOLO训练器...")
    
    # 检查环境
    if not check_environment():
        input("按回车键退出...")
        return 1
    
    # 创建必要的目录
    create_models_dir()
    
    try:
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("YOLO训练器")
        app.setApplicationVersion("1.0")
        app.setOrganizationName("YOLO Trainer")
        
        # 设置应用程序样式
        app.setStyle("Fusion")
        
        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        app.setFont(font)
        
        # 创建启动画面
        splash = create_splash_screen(app)
        
        # 延迟创建主窗口
        def create_main_window():
            try:
                splash.showMessage(
                    "YOLO可视化训练界面\n\n正在初始化界面...",
                    Qt.AlignCenter | Qt.AlignBottom,
                    Qt.black
                )
                app.processEvents()
                
                # 创建主窗口
                window = YOLOTrainerGUI()
                
                splash.showMessage(
                    "YOLO可视化训练界面\n\n启动完成!",
                    Qt.AlignCenter | Qt.AlignBottom,
                    Qt.black
                )
                app.processEvents()
                
                # 显示主窗口
                window.show()
                splash.finish(window)
                
                logger.info("YOLO训练器启动成功")
                
            except Exception as e:
                logger.error(f"启动失败: {e}")
                splash.close()
                QMessageBox.critical(
                    None, "启动失败", 
                    f"启动YOLO训练器失败:\n{str(e)}\n\n请检查日志文件获取详细信息。"
                )
                app.quit()
        
        # 使用定时器延迟创建主窗口
        QTimer.singleShot(1000, create_main_window)
        
        # 运行应用程序
        return app.exec()
        
    except Exception as e:
        logger.error(f"应用程序运行失败: {e}")
        print(f"错误: {e}")
        input("按回车键退出...")
        return 1


if __name__ == "__main__":
    sys.exit(main())
