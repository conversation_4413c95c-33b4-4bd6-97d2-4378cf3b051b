@echo off
echo YOLO可视化训练界面启动器
echo ========================

echo 检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo.
echo 检查依赖包...
python -c "import PySide6, ultralytics; print('依赖包检查通过')"
if %errorlevel% neq 0 (
    echo 错误: 缺少必要的依赖包
    echo 请运行: pip install -r requirements.txt
    pause
    exit /b 1
)

echo.
echo 启动YOLO训练器...
python run_yolo_trainer.py

pause
