#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有模型类型的下载和分析功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from model_downloader import ModelDownloader
from utils import setup_logging, get_model_info

def test_model_availability():
    """测试所有模型的可用性"""
    print("测试模型下载器中的模型配置...")
    
    downloader = ModelDownloader()
    
    # 按任务类型分组显示
    model_groups = {
        "目标检测": ["yolo11n.pt", "yolo11s.pt", "yolo11m.pt", "yolo11l.pt", "yolo11x.pt"],
        "实例分割": ["yolo11n-seg.pt", "yolo11s-seg.pt", "yolo11m-seg.pt", "yolo11l-seg.pt", "yolo11x-seg.pt"],
        "姿态估计": ["yolo11n-pose.pt", "yolo11s-pose.pt", "yolo11m-pose.pt", "yolo11l-pose.pt", "yolo11x-pose.pt"],
        "图像分类": ["yolo11n-cls.pt", "yolo11s-cls.pt", "yolo11m-cls.pt", "yolo11l-cls.pt", "yolo11x-cls.pt"],
        "旋转目标检测": ["yolo11n-obb.pt", "yolo11s-obb.pt", "yolo11m-obb.pt", "yolo11l-obb.pt", "yolo11x-obb.pt"]
    }
    
    total_models = 0
    available_models = 0
    
    for task_name, models in model_groups.items():
        print(f"\n{task_name}:")
        for model in models:
            total_models += 1
            if model in downloader.MODEL_URLS:
                print(f"  ✓ {model} - 可下载")
                available_models += 1
            else:
                print(f"  ❌ {model} - 不可下载")
    
    print(f"\n总结: {available_models}/{total_models} 个模型可下载")
    
    if available_models == total_models:
        print("🎉 所有模型都可以下载！")
        return True
    else:
        print("⚠️ 部分模型不可下载")
        return False

def test_model_analysis():
    """测试已下载模型的分析功能"""
    print("\n测试已下载模型的分析功能...")
    
    models_dir = Path("models")
    if not models_dir.exists():
        print("❌ models目录不存在")
        return False
    
    # 查找已下载的模型
    downloaded_models = list(models_dir.glob("*.pt"))
    
    if not downloaded_models:
        print("❌ 没有找到已下载的模型")
        print("请先下载一些模型进行测试")
        return False
    
    print(f"找到 {len(downloaded_models)} 个已下载的模型:")
    
    for model_path in downloaded_models:
        print(f"\n分析模型: {model_path.name}")
        
        try:
            model_info = get_model_info(str(model_path))
            
            print(f"  ✓ 分析成功")
            print(f"    任务类型: {model_info.get('task', 'unknown')}")
            print(f"    类别数量: {model_info.get('num_classes', 0)}")
            print(f"    文件大小: {model_info.get('size_formatted', 'unknown')}")
            print(f"    架构类型: {model_info.get('architecture', 'unknown')}")
            
        except Exception as e:
            print(f"  ❌ 分析失败: {e}")
    
    return True

def test_gui_model_list():
    """测试GUI中的模型列表"""
    print("\n测试GUI中的模型列表...")
    
    try:
        from yolo_trainer_gui import YOLOTrainerGUI
        from PySide6.QtWidgets import QApplication
        
        app = QApplication([])
        gui = YOLOTrainerGUI()
        
        # 获取模型下拉框中的所有项目
        model_combo = gui.model_combo
        model_count = model_combo.count()
        
        print(f"GUI中共有 {model_count} 个选项:")
        
        task_models = {
            "目标检测": 0,
            "实例分割": 0,
            "姿态估计": 0,
            "图像分类": 0,
            "旋转目标检测": 0,
            "分组标题": 0,
            "其他": 0
        }
        
        for i in range(model_count):
            item_text = model_combo.itemText(i)
            
            if item_text.startswith("---"):
                task_models["分组标题"] += 1
                print(f"  📁 {item_text}")
            elif item_text.endswith(".pt"):
                if "-seg" in item_text:
                    task_models["实例分割"] += 1
                elif "-pose" in item_text:
                    task_models["姿态估计"] += 1
                elif "-cls" in item_text:
                    task_models["图像分类"] += 1
                elif "-obb" in item_text:
                    task_models["旋转目标检测"] += 1
                else:
                    task_models["目标检测"] += 1
                print(f"    {item_text}")
            else:
                task_models["其他"] += 1
                print(f"  🔧 {item_text}")
        
        print(f"\n统计:")
        for task, count in task_models.items():
            if count > 0:
                print(f"  {task}: {count} 个")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def show_download_urls():
    """显示所有模型的下载URL"""
    print("\n所有模型的下载URL:")
    
    downloader = ModelDownloader()
    
    for model_name, url in downloader.MODEL_URLS.items():
        print(f"{model_name:20} -> {url}")

def main():
    """主测试函数"""
    setup_logging()
    
    print("YOLO所有模型类型测试")
    print("=" * 60)
    
    try:
        # 测试模型可用性
        models_available = test_model_availability()
        
        # 测试模型分析
        test_model_analysis()
        
        # 测试GUI模型列表
        test_gui_model_list()
        
        # 显示下载URL
        show_download_urls()
        
        print("\n" + "=" * 60)
        if models_available:
            print("🎉 所有测试完成！所有模型类型都支持下载。")
        else:
            print("⚠️ 测试完成，但部分模型不可下载。")
            
        print("\n现在您可以在GUI中选择以下任务类型的模型:")
        print("✓ 目标检测 (Detection)")
        print("✓ 实例分割 (Segmentation)")  
        print("✓ 姿态估计 (Pose)")
        print("✓ 图像分类 (Classification)")
        print("✓ 旋转目标检测 (OBB)")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
