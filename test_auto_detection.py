#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动识别功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils import get_model_info, auto_detect_dataset, analyze_model_task, setup_logging

def test_model_analysis():
    """测试模型分析功能"""
    print("=" * 50)
    print("测试模型分析功能")
    print("=" * 50)
    
    # 测试已下载的模型
    model_path = "models/yolo11n.pt"
    if os.path.exists(model_path):
        print(f"分析模型: {model_path}")
        model_info = get_model_info(model_path)
        
        print("模型信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
    else:
        print(f"模型文件不存在: {model_path}")
        print("请先下载模型或运行: python test_download.py")

def test_dataset_detection():
    """测试数据集检测功能"""
    print("\n" + "=" * 50)
    print("测试数据集检测功能")
    print("=" * 50)
    
    # 创建示例数据集结构用于测试
    test_dataset_path = Path("test_dataset")
    create_test_dataset(test_dataset_path)
    
    if test_dataset_path.exists():
        print(f"检测数据集: {test_dataset_path}")
        dataset_info = auto_detect_dataset(str(test_dataset_path))
        
        print("数据集信息:")
        for key, value in dataset_info.items():
            if isinstance(value, list) and len(value) > 5:
                print(f"  {key}: {value[:5]}... (共{len(value)}项)")
            else:
                print(f"  {key}: {value}")
    else:
        print("无法创建测试数据集")

def create_test_dataset(dataset_path: Path):
    """创建测试数据集结构"""
    try:
        # 创建目录结构
        for split in ['train', 'val']:
            images_dir = dataset_path / split / 'images'
            labels_dir = dataset_path / split / 'labels'
            images_dir.mkdir(parents=True, exist_ok=True)
            labels_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建示例文件
            for i in range(3):
                # 创建空的图像文件（仅用于测试）
                image_file = images_dir / f"image_{i:03d}.jpg"
                image_file.touch()
                
                # 创建示例标签文件
                label_file = labels_dir / f"image_{i:03d}.txt"
                with open(label_file, 'w') as f:
                    # YOLO格式: class_id x_center y_center width height
                    f.write("0 0.5 0.5 0.3 0.4\n")
                    if i % 2 == 0:  # 有些图像有多个对象
                        f.write("1 0.2 0.3 0.1 0.2\n")
        
        # 创建类别文件
        classes_file = dataset_path / 'classes.txt'
        with open(classes_file, 'w', encoding='utf-8') as f:
            f.write("person\n")
            f.write("car\n")
            
        print(f"✓ 创建测试数据集: {dataset_path}")
        
    except Exception as e:
        print(f"✗ 创建测试数据集失败: {e}")

def cleanup_test_dataset():
    """清理测试数据集"""
    test_dataset_path = Path("test_dataset")
    if test_dataset_path.exists():
        import shutil
        try:
            shutil.rmtree(test_dataset_path)
            print(f"✓ 清理测试数据集: {test_dataset_path}")
        except Exception as e:
            print(f"✗ 清理测试数据集失败: {e}")

def test_label_format_analysis():
    """测试标签格式分析"""
    print("\n" + "=" * 50)
    print("测试标签格式分析")
    print("=" * 50)
    
    from utils import analyze_label_format
    
    # 创建不同格式的测试标签
    test_labels = {
        "detect": "0 0.5 0.5 0.3 0.4",
        "segment": "0 0.5 0.5 0.3 0.4 0.1 0.1 0.9 0.1 0.9 0.9 0.1 0.9",
        "classify": "2",
        "pose": "0 0.5 0.5 0.3 0.4 " + " ".join([f"{i*0.1:.1f}" for i in range(34)])
    }
    
    for task, label_content in test_labels.items():
        test_file = Path(f"test_label_{task}.txt")
        try:
            with open(test_file, 'w') as f:
                f.write(label_content)
            
            result = analyze_label_format(test_file)
            print(f"{task:10} -> 检测结果: {result}")
            
            # 清理
            test_file.unlink()
            
        except Exception as e:
            print(f"{task:10} -> 分析失败: {e}")

def main():
    """主测试函数"""
    setup_logging()
    
    print("YOLO自动识别功能测试")
    print("=" * 50)
    
    try:
        # 测试模型分析
        test_model_analysis()
        
        # 测试标签格式分析
        test_label_format_analysis()
        
        # 测试数据集检测
        test_dataset_detection()
        
        print("\n" + "=" * 50)
        print("🎉 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        
    finally:
        # 清理测试文件
        cleanup_test_dataset()

if __name__ == "__main__":
    main()
