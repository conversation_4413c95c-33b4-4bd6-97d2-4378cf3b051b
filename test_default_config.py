#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认配置修改
验证训练参数的默认值是否正确设置
"""

import sys
import os
import platform
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication
from yolo_trainer_gui import YOLOTrainerGUI
from config_manager import ConfigManager
from utils import setup_logging

def test_gui_default_values(gui):
    """测试GUI界面的默认值"""
    print("测试GUI界面默认值...")
    
    # 检查批次大小默认值
    batch_value = gui.batch_spin.value()
    print(f"  批次大小默认值: {batch_value}")
    assert batch_value == 4, f"批次大小应为4，实际为{batch_value}"
    
    # 检查设备默认值
    device_value = gui.device_combo.currentText()
    print(f"  设备默认值: {device_value}")
    assert device_value == "0", f"设备应为'0'，实际为'{device_value}'"
    
    # 检查工作线程默认值
    workers_value = gui.workers_spin.value()
    print(f"  工作线程默认值: {workers_value}")
    assert workers_value == 0, f"工作线程应为0，实际为{workers_value}"
    
    # 检查警告标签
    warning_text = gui.workers_warning.text()
    print(f"  工作线程警告: {warning_text}")
    
    if platform.system() == "Windows":
        assert "Windows" in warning_text, "Windows系统应显示相关警告"
    else:
        assert "Linux/Mac" in warning_text or "CPU核心数" in warning_text, "非Windows系统应显示相关提示"
    
    print("  ✓ GUI界面默认值测试通过")
    return True

def test_config_manager_defaults():
    """测试配置管理器的默认值"""
    print("\n测试配置管理器默认值...")
    
    config_manager = ConfigManager()
    default_config = config_manager.get_default_config()
    
    # 检查批次大小
    batch_value = default_config.get('batch')
    print(f"  批次大小默认值: {batch_value}")
    assert batch_value == 4, f"批次大小应为4，实际为{batch_value}"
    
    # 检查设备
    device_value = default_config.get('device')
    print(f"  设备默认值: {device_value}")
    assert device_value == "0", f"设备应为'0'，实际为'{device_value}'"
    
    # 检查工作线程
    workers_value = default_config.get('workers')
    print(f"  工作线程默认值: {workers_value}")
    assert workers_value == 0, f"工作线程应为0，实际为{workers_value}"
    
    print("  ✓ 配置管理器默认值测试通过")
    return True

def test_workers_warning_logic(app, gui):
    """测试工作线程警告逻辑"""
    print("\n测试工作线程警告逻辑...")
    
    # 测试不同的工作线程值
    test_values = [0, 1, 4, 8]
    
    for value in test_values:
        print(f"  测试工作线程值: {value}")
        gui.workers_spin.setValue(value)
        
        # 触发值变化事件
        gui.on_workers_changed(value)
        
        warning_text = gui.workers_warning.text()
        warning_style = gui.workers_warning.styleSheet()
        
        print(f"    警告文本: {warning_text}")
        
        if platform.system() == "Windows":
            if value > 0:
                assert "建议设置为0" in warning_text or "避免多进程问题" in warning_text, "Windows系统大于0应显示警告"
                assert "#dc3545" in warning_style, "应显示红色警告"
            else:
                assert "推荐设置" in warning_text or "✓" in warning_text, "Windows系统设置为0应显示正确提示"
                assert "#28a745" in warning_style, "应显示绿色提示"
        else:
            if value == 0:
                assert "CPU核心数" in warning_text or "提高性能" in warning_text, "非Windows系统设置为0应提示可以设置更高值"
            else:
                assert f"{value}" in warning_text and "✓" in warning_text, "非Windows系统设置大于0应显示使用的线程数"
    
    print("  ✓ 工作线程警告逻辑测试通过")
    return True

def test_system_detection():
    """测试系统检测"""
    print("\n测试系统检测...")
    
    current_system = platform.system()
    print(f"  当前系统: {current_system}")
    
    if current_system == "Windows":
        print("  ✓ 检测到Windows系统，应显示相关警告")
    elif current_system in ["Linux", "Darwin"]:  # Darwin是macOS
        print("  ✓ 检测到Linux/Mac系统，应显示性能提示")
    else:
        print(f"  ⚠️ 未知系统: {current_system}")
    
    return True

def test_config_consistency(app, gui):
    """测试配置一致性"""
    print("\n测试配置一致性...")
    config_manager = ConfigManager()
    
    # 获取GUI配置
    gui_config = gui.get_training_config()
    
    # 获取默认配置
    default_config = config_manager.get_default_config()
    
    # 检查关键参数的一致性
    key_params = ['batch', 'device', 'workers']
    
    for param in key_params:
        gui_value = gui_config.get(param)
        default_value = default_config.get(param)
        
        print(f"  {param}: GUI={gui_value}, 默认={default_value}")
        
        # 对于字符串类型的参数，需要转换比较
        if param == 'device':
            assert str(gui_value) == str(default_value), f"{param}参数不一致"
        else:
            assert gui_value == default_value, f"{param}参数不一致"
    
    print("  ✓ 配置一致性测试通过")
    return True

def show_config_summary():
    """显示配置总结"""
    print("\n" + "=" * 50)
    print("默认配置修改总结")
    print("=" * 50)
    
    print("修改的参数:")
    print("  • 批次大小 (batch): 16 → 4")
    print("  • 设备 (device): 'auto' → '0'")
    print("  • 工作线程 (workers): 8 → 0")
    
    print("\n新增功能:")
    print("  • Windows系统工作线程警告")
    print("  • 动态警告文本和颜色")
    print("  • 系统自适应提示")
    
    print(f"\n当前系统: {platform.system()}")
    if platform.system() == "Windows":
        print("  • 显示Windows系统特定警告")
        print("  • 建议工作线程设置为0")
    else:
        print("  • 显示Linux/Mac系统提示")
        print("  • 建议根据CPU核心数设置工作线程")

def main():
    """主测试函数"""
    setup_logging()
    
    print("YOLO训练器默认配置修改测试")
    print("=" * 50)
    
    try:
        # 创建单一的应用程序实例
        app = QApplication([])
        gui = YOLOTrainerGUI()

        # 测试GUI默认值
        test_gui_default_values(gui)

        # 测试配置管理器默认值
        test_config_manager_defaults()

        # 测试工作线程警告逻辑
        test_workers_warning_logic(app, gui)

        # 测试系统检测
        test_system_detection()

        # 测试配置一致性
        test_config_consistency(app, gui)

        # 显示配置总结
        show_config_summary()

        print("\n" + "=" * 50)
        print("🎉 所有测试通过！默认配置修改成功。")

        app.quit()

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
