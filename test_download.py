#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型下载功能
"""

import sys
import os
import time
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtCore import QCoreApplication
from model_downloader import ModelDownloader
from utils import setup_logging

def test_download():
    """测试下载功能"""
    setup_logging()
    
    # 创建应用程序（需要用于Qt信号）
    app = QCoreApplication(sys.argv)
    
    # 创建下载器
    downloader = ModelDownloader()
    
    # 连接信号
    def on_progress(progress):
        print(f"下载进度: {progress}%")
    
    def on_finished(path):
        print(f"下载完成: {path}")
        app.quit()
    
    def on_error(error):
        print(f"下载失败: {error}")
        app.quit()
    
    downloader.progress_updated.connect(on_progress)
    downloader.download_finished.connect(on_finished)
    downloader.download_error.connect(on_error)
    
    # 开始下载最小的模型
    print("开始下载 yolo11n.pt...")
    downloader.download_model("yolo11n.pt")
    
    # 运行事件循环
    app.exec()

if __name__ == "__main__":
    test_download()
