#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试下载分割模型
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtCore import QCoreApplication
from model_downloader import ModelDownloader
from utils import setup_logging, get_model_info

def test_download_segment_model():
    """测试下载分割模型"""
    setup_logging()
    
    # 创建应用程序（需要用于Qt信号）
    app = QCoreApplication(sys.argv)
    
    # 创建下载器
    downloader = ModelDownloader()
    
    # 选择一个小的分割模型进行测试
    model_name = "yolo11n-seg.pt"
    
    print(f"测试下载分割模型: {model_name}")
    
    # 连接信号
    def on_progress(progress):
        print(f"下载进度: {progress}%")
    
    def on_finished(path):
        print(f"✓ 下载完成: {path}")
        
        # 分析下载的模型
        print("分析模型...")
        try:
            model_info = get_model_info(path)
            print(f"  任务类型: {model_info.get('task', 'unknown')}")
            print(f"  类别数量: {model_info.get('num_classes', 0)}")
            print(f"  文件大小: {model_info.get('size_formatted', 'unknown')}")
            print(f"  架构类型: {model_info.get('architecture', 'unknown')}")
            
            if 'num_masks' in model_info:
                print(f"  Mask数量: {model_info.get('num_masks', 0)}")
                
        except Exception as e:
            print(f"  ❌ 模型分析失败: {e}")
        
        app.quit()
    
    def on_error(error):
        print(f"❌ 下载失败: {error}")
        app.quit()
    
    downloader.progress_updated.connect(on_progress)
    downloader.download_finished.connect(on_finished)
    downloader.download_error.connect(on_error)
    
    # 检查模型是否已存在
    if downloader.is_model_downloaded(model_name):
        print(f"模型 {model_name} 已存在，直接分析...")
        model_path = downloader.get_model_path(model_name)
        on_finished(model_path)
    else:
        print("开始下载...")
        downloader.download_model(model_name)
        
        # 运行事件循环
        app.exec()

if __name__ == "__main__":
    test_download_segment_model()
