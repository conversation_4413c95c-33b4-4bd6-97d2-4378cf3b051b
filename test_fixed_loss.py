#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的损失提取功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from training_worker import TrainingWorker

def test_detection_loss():
    """测试检测任务损失提取"""
    print("1. 测试检测任务损失提取")
    print("-" * 40)
    
    # 创建训练工作器
    config = {'epochs': 100}
    worker = TrainingWorker(config)
    
    # 模拟检测任务的trainer
    class MockDetectionTrainer:
        def __init__(self):
            self.epoch = 0
            self.loss_items = np.array([2.4775, 6.9256, 2.6429])  # box, cls, dfl
            self.loss_names = ('box_loss', 'cls_loss', 'dfl_loss')
            self.tloss = None
    
    trainer = MockDetectionTrainer()
    worker.current_epoch = 1
    
    # 提取损失
    epoch_metrics = {}
    try:
        loss_items = trainer.loss_items
        
        # 根据YOLO11的损失顺序解析
        if isinstance(loss_items, (list, tuple, np.ndarray)) and len(loss_items) >= 3:
            # 检查是否有loss_names来确定损失类型
            loss_names = getattr(trainer, 'loss_names', None)
            
            if loss_names and len(loss_names) == len(loss_items):
                # 根据loss_names映射损失
                for i, name in enumerate(loss_names):
                    if i < len(loss_items):
                        epoch_metrics[name] = float(loss_items[i])
            else:
                # 默认映射（根据长度判断任务类型）
                if len(loss_items) >= 4:
                    # 分割任务：box, seg, cls, dfl
                    epoch_metrics['box_loss'] = float(loss_items[0])
                    epoch_metrics['seg_loss'] = float(loss_items[1])
                    epoch_metrics['cls_loss'] = float(loss_items[2])
                    epoch_metrics['dfl_loss'] = float(loss_items[3])
                elif len(loss_items) == 3:
                    # 检测任务：box, cls, dfl
                    epoch_metrics['box_loss'] = float(loss_items[0])
                    epoch_metrics['cls_loss'] = float(loss_items[1])
                    epoch_metrics['dfl_loss'] = float(loss_items[2])
            
            # 计算总损失
            epoch_metrics['train_loss'] = sum(float(x) for x in loss_items)
    
    except Exception as e:
        print(f"  ❌ 提取失败: {e}")
        return False
    
    # 验证结果
    expected = {
        'box_loss': 2.4775,
        'cls_loss': 6.9256,
        'dfl_loss': 2.6429,
        'train_loss': 12.046
    }
    
    print("  提取的损失:")
    success = True
    for key, expected_value in expected.items():
        if key in epoch_metrics:
            actual_value = epoch_metrics[key]
            if abs(actual_value - expected_value) < 1e-3:
                print(f"    ✓ {key}: {actual_value:.4f}")
            else:
                print(f"    ❌ {key}: 期望 {expected_value:.4f}, 实际 {actual_value:.4f}")
                success = False
        else:
            print(f"    ❌ {key}: 缺失")
            success = False
    
    return success

def test_segmentation_loss():
    """测试分割任务损失提取"""
    print("\n2. 测试分割任务损失提取")
    print("-" * 40)
    
    # 模拟分割任务的trainer
    class MockSegmentationTrainer:
        def __init__(self):
            self.epoch = 0
            self.loss_items = np.array([1.376, 2.616, 4.629, 1.207])  # box, seg, cls, dfl
            self.loss_names = ('box_loss', 'seg_loss', 'cls_loss', 'dfl_loss')
            self.tloss = None
    
    trainer = MockSegmentationTrainer()
    
    # 提取损失
    epoch_metrics = {}
    try:
        loss_items = trainer.loss_items
        
        if isinstance(loss_items, (list, tuple, np.ndarray)) and len(loss_items) >= 3:
            loss_names = getattr(trainer, 'loss_names', None)
            
            if loss_names and len(loss_names) == len(loss_items):
                for i, name in enumerate(loss_names):
                    if i < len(loss_items):
                        epoch_metrics[name] = float(loss_items[i])
            else:
                if len(loss_items) >= 4:
                    epoch_metrics['box_loss'] = float(loss_items[0])
                    epoch_metrics['seg_loss'] = float(loss_items[1])
                    epoch_metrics['cls_loss'] = float(loss_items[2])
                    epoch_metrics['dfl_loss'] = float(loss_items[3])
                elif len(loss_items) == 3:
                    epoch_metrics['box_loss'] = float(loss_items[0])
                    epoch_metrics['cls_loss'] = float(loss_items[1])
                    epoch_metrics['dfl_loss'] = float(loss_items[2])
            
            epoch_metrics['train_loss'] = sum(float(x) for x in loss_items)
    
    except Exception as e:
        print(f"  ❌ 提取失败: {e}")
        return False
    
    # 验证结果
    expected = {
        'box_loss': 1.376,
        'seg_loss': 2.616,
        'cls_loss': 4.629,
        'dfl_loss': 1.207,
        'train_loss': 9.828
    }
    
    print("  提取的损失:")
    success = True
    for key, expected_value in expected.items():
        if key in epoch_metrics:
            actual_value = epoch_metrics[key]
            if abs(actual_value - expected_value) < 1e-3:
                print(f"    ✓ {key}: {actual_value:.4f}")
            else:
                print(f"    ❌ {key}: 期望 {expected_value:.4f}, 实际 {actual_value:.4f}")
                success = False
        else:
            print(f"    ❌ {key}: 缺失")
            success = False
    
    return success

def test_multi_plot_integration():
    """测试与多标签页组件的集成"""
    print("\n3. 测试多标签页组件集成")
    print("-" * 40)
    
    try:
        from PySide6.QtWidgets import QApplication
        from multi_plot_widget import MultiPlotWidget
        
        app = QApplication([])
        
        # 创建多标签页组件
        multi_plot = MultiPlotWidget()
        
        # 测试检测任务数据
        detection_metrics = {
            'box_loss': 2.4775,
            'cls_loss': 6.9256,
            'dfl_loss': 2.6429,
            'train_loss': 12.046,
            'box_mAP50': 0.0526,
            'box_precision': 0.0449,
        }
        
        multi_plot.add_epoch_data(1, detection_metrics)
        print("  ✓ 检测任务数据添加成功")
        
        # 测试分割任务数据
        segmentation_metrics = {
            'box_loss': 1.376,
            'seg_loss': 2.616,
            'cls_loss': 4.629,
            'dfl_loss': 1.207,
            'train_loss': 9.828,
            'box_mAP50': 0.1266,
            'seg_mAP50': 0.1000,
            'box_precision': 0.0400,
            'seg_precision': 0.0318,
        }
        
        multi_plot.add_epoch_data(2, segmentation_metrics)
        print("  ✓ 分割任务数据添加成功")
        
        # 检查损失函数标签页
        loss_tab = None
        for tab_name, plot_widget in multi_plot.plot_widgets.items():
            if tab_name == "损失函数":
                loss_tab = plot_widget
                break
        
        if loss_tab:
            stored_metrics = list(loss_tab.metrics_data.keys())
            print(f"  ✓ 损失函数标签页存储的指标: {stored_metrics}")
            
            # 检查是否包含期望的损失指标
            expected_losses = ['box_loss', 'cls_loss', 'dfl_loss', 'train_loss']
            missing = [loss for loss in expected_losses if loss not in stored_metrics]
            
            if missing:
                print(f"  ❌ 缺失的损失指标: {missing}")
                return False
            else:
                print("  ✓ 所有基础损失指标都已存储")
                
            # 检查分割损失
            if 'seg_loss' in stored_metrics:
                print("  ✓ 分割损失指标也已存储")
            
            return True
        else:
            print("  ❌ 未找到损失函数标签页")
            return False
        
        app.quit()
        
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")
        return False

def show_usage_summary():
    """显示使用总结"""
    print("\n" + "=" * 60)
    print("损失函数曲线图修复总结")
    print("=" * 60)
    
    print("🔧 修复内容:")
    print("  1. 支持检测任务的3个损失: box_loss, cls_loss, dfl_loss")
    print("  2. 支持分割任务的4个损失: box_loss, seg_loss, cls_loss, dfl_loss")
    print("  3. 自动识别任务类型并正确映射损失名称")
    print("  4. 使用trainer.loss_names确保准确映射")
    
    print("\n📊 支持的损失类型:")
    print("  检测任务:")
    print("    - box_loss: 边界框损失")
    print("    - cls_loss: 分类损失")
    print("    - dfl_loss: DFL损失")
    print("    - train_loss: 总训练损失")
    
    print("  分割任务:")
    print("    - box_loss: 边界框损失")
    print("    - seg_loss: 分割损失")
    print("    - cls_loss: 分类损失")
    print("    - dfl_loss: DFL损失")
    print("    - train_loss: 总训练损失")
    
    print("\n🎯 现在的效果:")
    print("  ✅ 检测训练: 显示3种损失曲线")
    print("  ✅ 分割训练: 显示4种损失曲线")
    print("  ✅ 实时更新: 每个epoch完成后立即更新")
    print("  ✅ 正确分组: 所有损失显示在'损失函数'标签页")
    
    print("\n🚀 立即测试:")
    print("  现在重新启动您的YOLO分割训练，")
    print("  损失函数标签页将正确显示所有损失曲线！")

def main():
    """主函数"""
    print("YOLO训练曲线图 - 损失提取功能修复测试")
    print("=" * 60)
    
    success = True
    
    # 测试检测任务
    if not test_detection_loss():
        success = False
    
    # 测试分割任务
    if not test_segmentation_loss():
        success = False
    
    # 测试集成
    if not test_multi_plot_integration():
        success = False
    
    # 显示总结
    show_usage_summary()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！损失提取功能修复成功。")
    else:
        print("❌ 部分测试失败，请检查上述错误信息。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
