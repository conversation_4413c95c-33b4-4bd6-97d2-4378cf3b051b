#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的多标签页训练曲线图功能
"""

import sys
import time
import random
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QLabel
from PySide6.QtCore import QTimer, Signal, QObject
from multi_plot_widget import MultiPlotWidget

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("修复后的多标签页训练曲线图测试")
        self.setGeometry(100, 100, 1200, 800)
        
        self.current_epoch = 0
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("就绪 - 点击开始测试按钮")
        self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; color: #2196F3; }")
        layout.addWidget(self.status_label)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始测试")
        self.start_btn.clicked.connect(self.start_test)
        
        self.stop_btn = QPushButton("停止测试")
        self.stop_btn.clicked.connect(self.stop_test)
        self.stop_btn.setEnabled(False)
        
        self.clear_btn = QPushButton("清空数据")
        self.clear_btn.clicked.connect(self.clear_data)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.clear_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 多标签页训练曲线图
        self.multi_plot = MultiPlotWidget()
        layout.addWidget(self.multi_plot)
        
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulate_epoch)
        
    def start_test(self):
        """开始测试"""
        self.current_epoch = 0
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 清空数据
        self.multi_plot.clear_all_data()
        self.status_label.setText("测试中... 正在生成模拟数据")
        self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; color: #FF9800; }")
        
        # 启动定时器，每300ms模拟一个epoch
        self.timer.start(300)
        
    def stop_test(self):
        """停止测试"""
        self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.status_label.setText(f"测试停止 - 已完成 {self.current_epoch} 个epoch")
        self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; color: #f44336; }")
        
    def clear_data(self):
        """清空数据"""
        self.multi_plot.clear_all_data()
        self.current_epoch = 0
        self.status_label.setText("数据已清空 - 点击开始测试按钮")
        self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; color: #2196F3; }")
        
    def simulate_epoch(self):
        """模拟一个训练轮次"""
        if self.current_epoch >= 50:  # 限制为50个epoch
            self.stop_test()
            self.status_label.setText("测试完成 - 所有数据已生成")
            self.status_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; color: #4CAF50; }")
            return
            
        self.current_epoch += 1
        
        # 生成模拟的训练指标
        metrics = self.generate_realistic_metrics(self.current_epoch)
        
        # 直接调用多标签页组件的方法
        self.multi_plot.add_epoch_data(self.current_epoch, metrics)
        
        # 更新状态
        self.status_label.setText(f"测试中... Epoch {self.current_epoch}/50")
        
    def generate_realistic_metrics(self, epoch: int) -> dict:
        """生成真实的训练指标"""
        progress = epoch / 50.0
        
        # 基础趋势函数
        def improve_trend(start, end, rate=2):
            return start + (end - start) * (1 - 1 / (1 + progress * rate))
        
        def decline_trend(start, end, rate=2):
            return start - (start - end) * (1 - 1 / (1 + progress * rate))
        
        # 添加真实的随机波动
        def add_realistic_noise(value, noise=0.03):
            return max(0, value + random.uniform(-noise, noise))
        
        metrics = {}
        
        # mAP指标 (逐渐提高，但有波动)
        metrics['box_mAP50'] = add_realistic_noise(improve_trend(0.15, 0.82))
        metrics['box_mAP50-95'] = add_realistic_noise(improve_trend(0.08, 0.58))
        
        # 精度召回 (逐渐提高)
        metrics['box_precision'] = add_realistic_noise(improve_trend(0.25, 0.88))
        metrics['box_recall'] = add_realistic_noise(improve_trend(0.20, 0.85))
        
        # 损失函数 (逐渐降低)
        metrics['train_loss'] = add_realistic_noise(decline_trend(2.8, 0.4), 0.1)
        metrics['val_loss'] = add_realistic_noise(decline_trend(2.5, 0.45), 0.08)
        metrics['box_loss'] = add_realistic_noise(decline_trend(1.6, 0.25), 0.05)
        metrics['cls_loss'] = add_realistic_noise(decline_trend(1.3, 0.18), 0.04)
        metrics['dfl_loss'] = add_realistic_noise(decline_trend(0.9, 0.12), 0.03)
        
        # 在后期添加分割指标
        if epoch > 15:
            metrics['seg_mAP50'] = add_realistic_noise(improve_trend(0.12, 0.75))
            metrics['seg_mAP50-95'] = add_realistic_noise(improve_trend(0.06, 0.52))
            metrics['seg_precision'] = add_realistic_noise(improve_trend(0.22, 0.83))
            metrics['seg_recall'] = add_realistic_noise(improve_trend(0.18, 0.80))
            metrics['seg_loss'] = add_realistic_noise(decline_trend(1.4, 0.28), 0.05)
        
        # 在后期添加分类指标
        if epoch > 30:
            metrics['top1_acc'] = add_realistic_noise(improve_trend(0.35, 0.92))
            metrics['top5_acc'] = add_realistic_noise(improve_trend(0.55, 0.98))
        
        return metrics

def main():
    """主函数"""
    print("修复后的多标签页训练曲线图测试")
    print("=" * 50)
    
    # 检查依赖
    try:
        import matplotlib
        print(f"✓ matplotlib版本: {matplotlib.__version__}")
    except ImportError:
        print("❌ matplotlib不可用")
        return 1
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    window = TestMainWindow()
    window.show()
    
    print("\n测试窗口已启动！")
    print("功能说明:")
    print("1. 点击'开始测试'按钮开始生成模拟数据")
    print("2. 观察4个标签页中的实时曲线更新:")
    print("   - mAP指标: 显示各种mAP值的提升趋势")
    print("   - 精度召回: 显示precision和recall的改善")
    print("   - 损失函数: 显示各种loss的下降趋势")
    print("   - 分类准确率: 显示准确率的提升(后期出现)")
    print("3. 可以随时停止测试或清空数据")
    print("4. 每个标签页都支持独立的指标选择")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
