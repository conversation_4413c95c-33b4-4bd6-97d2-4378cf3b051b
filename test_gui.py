#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI应用程序
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """测试所有导入"""
    print("测试导入...")
    
    try:
        import PySide6
        print("✓ PySide6 导入成功")
    except ImportError as e:
        print(f"✗ PySide6 导入失败: {e}")
        return False
    
    try:
        import ultralytics
        print(f"✓ ultralytics 导入成功, 版本: {ultralytics.__version__}")
    except ImportError as e:
        print(f"✗ ultralytics 导入失败: {e}")
        return False
    
    try:
        from utils import setup_logging, check_dependencies
        print("✓ utils 模块导入成功")
    except ImportError as e:
        print(f"✗ utils 模块导入失败: {e}")
        return False
    
    try:
        from model_downloader import ModelDownloader
        print("✓ model_downloader 模块导入成功")
    except ImportError as e:
        print(f"✗ model_downloader 模块导入失败: {e}")
        return False
    
    try:
        from training_worker import TrainingWorker
        print("✓ training_worker 模块导入成功")
    except ImportError as e:
        print(f"✗ training_worker 模块导入失败: {e}")
        return False
    
    try:
        from config_manager import ConfigManager
        print("✓ config_manager 模块导入成功")
    except ImportError as e:
        print(f"✗ config_manager 模块导入失败: {e}")
        return False
    
    return True

def test_gui_creation():
    """测试GUI创建"""
    print("\n测试GUI创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from yolo_trainer_gui import YOLOTrainerGUI
        
        app = QApplication(sys.argv)
        window = YOLOTrainerGUI()
        print("✓ GUI窗口创建成功")
        
        # 不显示窗口，只测试创建
        app.quit()
        return True
        
    except Exception as e:
        print(f"✗ GUI创建失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    try:
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试默认配置
        default_config = config_manager.get_default_config()
        print(f"✓ 默认配置获取成功，包含 {len(default_config)} 个参数")
        
        # 测试配置验证（不要求data参数）
        errors = config_manager.validate_config(default_config, require_data=False)
        if not errors:
            print("✓ 默认配置验证通过")
        else:
            print(f"✗ 默认配置验证失败: {errors}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理器测试失败: {e}")
        return False

def test_model_downloader():
    """测试模型下载器"""
    print("\n测试模型下载器...")
    
    try:
        from model_downloader import ModelDownloader
        
        downloader = ModelDownloader()
        print("✓ 模型下载器创建成功")
        
        # 检查模型URL
        if downloader.MODEL_URLS:
            print(f"✓ 模型URL配置正确，包含 {len(downloader.MODEL_URLS)} 个模型")
        else:
            print("✗ 模型URL配置为空")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 模型下载器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("YOLO训练器GUI测试")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        return 1
    
    # 测试配置管理器
    if not test_config_manager():
        print("\n❌ 配置管理器测试失败")
        return 1
    
    # 测试模型下载器
    if not test_model_downloader():
        print("\n❌ 模型下载器测试失败")
        return 1
    
    # 测试GUI创建
    if not test_gui_creation():
        print("\n❌ GUI创建测试失败")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 所有测试通过！应用程序可以正常运行。")
    print("\n要启动完整的GUI应用程序，请运行:")
    print("python run_yolo_trainer.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
