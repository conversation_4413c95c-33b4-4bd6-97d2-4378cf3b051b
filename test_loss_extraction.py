#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试损失函数提取功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from training_worker import TrainingWorker

def test_loss_extraction():
    """测试损失函数提取"""
    print("测试损失函数提取功能")
    print("=" * 50)
    
    # 创建训练工作器实例
    config = {'epochs': 100}
    worker = TrainingWorker(config)
    
    # 模拟YOLO训练过程中的损失数据
    print("1. 测试训练损失提取:")
    
    # 模拟trainer对象
    class MockTrainer:
        def __init__(self, loss_items, tloss=None):
            self.loss_items = loss_items
            self.tloss = tloss
            self.epoch = 0
    
    # 测试不同格式的损失数据
    test_cases = [
        {
            'name': 'YOLO11分割任务损失 (numpy数组)',
            'loss_items': np.array([1.376, 2.616, 4.629, 1.207]),  # box, seg, cls, dfl
            'expected': {
                'box_loss': 1.376,
                'seg_loss': 2.616,
                'cls_loss': 4.629,
                'dfl_loss': 1.207,
                'train_loss': 9.828
            }
        },
        {
            'name': 'YOLO11分割任务损失 (列表)',
            'loss_items': [1.285, 2.251, 4.334, 1.138],
            'expected': {
                'box_loss': 1.285,
                'seg_loss': 2.251,
                'cls_loss': 4.334,
                'dfl_loss': 1.138,
                'train_loss': 9.008
            }
        },
        {
            'name': '总损失 (单个值)',
            'loss_items': 5.234,
            'expected': {
                'train_loss': 5.234
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n  测试 {i}: {test_case['name']}")
        
        # 模拟trainer
        trainer = MockTrainer(test_case['loss_items'])
        worker.current_epoch = 1
        
        # 提取损失
        epoch_metrics = {}
        try:
            loss_items = trainer.loss_items
            
            # 如果是numpy数组，模拟tensor的行为
            if isinstance(loss_items, np.ndarray):
                # 模拟tensor.cpu().numpy()的结果
                pass
            
            # 根据YOLO11的损失顺序解析
            if isinstance(loss_items, (list, tuple, np.ndarray)) and len(loss_items) >= 4:
                epoch_metrics['box_loss'] = float(loss_items[0])
                epoch_metrics['seg_loss'] = float(loss_items[1])
                epoch_metrics['cls_loss'] = float(loss_items[2])
                epoch_metrics['dfl_loss'] = float(loss_items[3])
                epoch_metrics['train_loss'] = sum(float(x) for x in loss_items[:4])
            elif isinstance(loss_items, (int, float)):
                epoch_metrics['train_loss'] = float(loss_items)
        
        except Exception as e:
            print(f"    ❌ 提取失败: {e}")
            continue
        
        # 验证结果
        expected = test_case['expected']
        success = True
        
        for key, expected_value in expected.items():
            if key in epoch_metrics:
                actual_value = epoch_metrics[key]
                if abs(actual_value - expected_value) < 1e-6:
                    print(f"    ✓ {key}: {actual_value:.4f}")
                else:
                    print(f"    ❌ {key}: 期望 {expected_value:.4f}, 实际 {actual_value:.4f}")
                    success = False
            else:
                print(f"    ❌ {key}: 缺失")
                success = False
        
        if success:
            print(f"    ✅ 测试 {i} 通过")
        else:
            print(f"    ❌ 测试 {i} 失败")

def test_validation_loss():
    """测试验证损失提取"""
    print("\n2. 测试验证损失提取:")
    
    # 模拟validator对象
    class MockValidator:
        def __init__(self, loss=None):
            self.loss = loss
    
    test_cases = [
        {
            'name': '验证损失 (numpy数组)',
            'loss': np.array([0.856, 1.234, 2.567, 0.789]),
            'expected': {
                'val_box_loss': 0.856,
                'val_seg_loss': 1.234,
                'val_cls_loss': 2.567,
                'val_dfl_loss': 0.789,
                'val_loss': 5.446
            }
        },
        {
            'name': '验证总损失 (单个值)',
            'loss': 3.456,
            'expected': {
                'val_loss': 3.456
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n  测试 {i}: {test_case['name']}")
        
        validator = MockValidator(test_case['loss'])
        plot_metrics = {}
        
        try:
            if hasattr(validator, 'loss') and validator.loss is not None:
                val_loss = validator.loss
                
                if isinstance(val_loss, (list, tuple, np.ndarray)) and len(val_loss) >= 4:
                    plot_metrics['val_box_loss'] = float(val_loss[0])
                    plot_metrics['val_seg_loss'] = float(val_loss[1])
                    plot_metrics['val_cls_loss'] = float(val_loss[2])
                    plot_metrics['val_dfl_loss'] = float(val_loss[3])
                    plot_metrics['val_loss'] = sum(float(x) for x in val_loss[:4])
                elif isinstance(val_loss, (int, float)):
                    plot_metrics['val_loss'] = float(val_loss)
        
        except Exception as e:
            print(f"    ❌ 提取失败: {e}")
            continue
        
        # 验证结果
        expected = test_case['expected']
        success = True
        
        for key, expected_value in expected.items():
            if key in plot_metrics:
                actual_value = plot_metrics[key]
                if abs(actual_value - expected_value) < 1e-6:
                    print(f"    ✓ {key}: {actual_value:.4f}")
                else:
                    print(f"    ❌ {key}: 期望 {expected_value:.4f}, 实际 {actual_value:.4f}")
                    success = False
            else:
                print(f"    ❌ {key}: 缺失")
                success = False
        
        if success:
            print(f"    ✅ 测试 {i} 通过")
        else:
            print(f"    ❌ 测试 {i} 失败")

def test_multi_plot_loss_display():
    """测试多标签页组件的损失显示"""
    print("\n3. 测试多标签页损失显示:")
    
    try:
        from PySide6.QtWidgets import QApplication
        from multi_plot_widget import MultiPlotWidget
        
        app = QApplication([])
        
        # 创建多标签页组件
        multi_plot = MultiPlotWidget()
        
        # 模拟完整的损失数据
        loss_metrics = {
            # 训练损失
            'train_loss': 9.828,
            'box_loss': 1.376,
            'seg_loss': 2.616,
            'cls_loss': 4.629,
            'dfl_loss': 1.207,
            
            # 验证损失
            'val_loss': 5.446,
            'val_box_loss': 0.856,
            'val_seg_loss': 1.234,
            'val_cls_loss': 2.567,
            'val_dfl_loss': 0.789,
        }
        
        # 添加数据
        multi_plot.add_epoch_data(1, loss_metrics)
        
        # 检查损失函数标签页
        loss_tab = None
        for tab_name, plot_widget in multi_plot.plot_widgets.items():
            if tab_name == "损失函数":
                loss_tab = plot_widget
                break
        
        if loss_tab:
            stored_metrics = list(loss_tab.metrics_data.keys())
            print(f"  损失函数标签页存储的指标: {stored_metrics}")
            
            expected_loss_metrics = [
                'train_loss', 'val_loss', 'box_loss', 'seg_loss', 
                'cls_loss', 'dfl_loss', 'val_box_loss', 'val_seg_loss',
                'val_cls_loss', 'val_dfl_loss'
            ]
            
            missing_metrics = []
            for metric in expected_loss_metrics:
                if metric in loss_metrics and metric not in stored_metrics:
                    missing_metrics.append(metric)
            
            if missing_metrics:
                print(f"  ❌ 缺失的损失指标: {missing_metrics}")
            else:
                print(f"  ✅ 所有损失指标都正确存储")
                
            # 检查数据值
            for metric in stored_metrics:
                if metric in loss_metrics:
                    expected_value = loss_metrics[metric]
                    actual_values = loss_tab.metrics_data[metric]
                    if actual_values and len(actual_values) > 0:
                        actual_value = actual_values[0]
                        if abs(actual_value - expected_value) < 1e-6:
                            print(f"    ✓ {metric}: {actual_value:.4f}")
                        else:
                            print(f"    ❌ {metric}: 期望 {expected_value:.4f}, 实际 {actual_value:.4f}")
        else:
            print("  ❌ 未找到损失函数标签页")
        
        app.quit()
        
    except Exception as e:
        print(f"  ❌ 多标签页测试失败: {e}")

def show_loss_guide():
    """显示损失函数使用指南"""
    print("\n" + "=" * 60)
    print("损失函数曲线图使用指南")
    print("=" * 60)
    
    print("📊 现在支持的损失指标:")
    print("  训练损失:")
    print("    - train_loss: 总训练损失")
    print("    - box_loss: 边界框损失")
    print("    - seg_loss: 分割损失")
    print("    - cls_loss: 分类损失")
    print("    - dfl_loss: DFL损失")
    
    print("  验证损失:")
    print("    - val_loss: 总验证损失")
    print("    - val_box_loss: 验证边界框损失")
    print("    - val_seg_loss: 验证分割损失")
    print("    - val_cls_loss: 验证分类损失")
    print("    - val_dfl_loss: 验证DFL损失")
    
    print("\n🎨 颜色方案:")
    print("  - train_loss: 红色")
    print("  - val_loss: 蓝色")
    print("  - box_loss: 浅红色")
    print("  - seg_loss: 浅绿色")
    print("  - cls_loss: 浅橙色")
    print("  - dfl_loss: 浅紫色")
    print("  - val_*_loss: 对应颜色的更浅版本")
    
    print("\n🚀 使用方法:")
    print("  1. 训练开始后，损失曲线会实时更新")
    print("  2. 在'损失函数'标签页中查看所有损失指标")
    print("  3. 可以选择显示/隐藏特定的损失类型")
    print("  4. 观察训练和验证损失的收敛趋势")

def main():
    """主函数"""
    print("YOLO训练曲线图 - 损失函数提取测试")
    print("=" * 60)
    
    # 测试损失提取
    test_loss_extraction()
    
    # 测试验证损失
    test_validation_loss()
    
    # 测试多标签页显示
    test_multi_plot_loss_display()
    
    # 显示使用指南
    show_loss_guide()
    
    print("\n" + "=" * 60)
    print("🎉 损失函数提取功能测试完成！")
    print("\n💡 现在您的训练过程中应该能看到完整的损失曲线了。")
    print("   请重新启动训练来测试实际效果。")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
