#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试YOLO指标转换功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from training_worker import TrainingWorker

def test_metric_conversion():
    """测试指标转换功能"""
    print("测试YOLO指标转换功能")
    print("=" * 50)
    
    # 创建训练工作器实例
    config = {'epochs': 100}
    worker = TrainingWorker(config)
    
    # 模拟YOLO训练日志中的指标数据
    yolo_metrics = {
        'fitness': 0.0623,
        'metrics/mAP50(B)': 0.0565,
        'metrics/mAP50(M)': 0.0391,
        'metrics/mAP50-95(B)': 0.0417,
        'metrics/mAP50-95(M)': 0.0169,
        'metrics/precision(B)': 0.0506,
        'metrics/precision(M)': 0.0341,
        'metrics/recall(B)': 0.1292,
        'metrics/recall(M)': 0.0979,
        'train/box_loss': 1.234,
        'train/seg_loss': 0.876,
        'train/cls_loss': 0.543,
        'train/dfl_loss': 0.321,
    }
    
    print("原始YOLO指标:")
    for key, value in yolo_metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # 转换指标
    converted_metrics = worker.convert_yolo_metrics(yolo_metrics)
    
    print("\n转换后的指标:")
    for key, value in converted_metrics.items():
        print(f"  {key}: {value:.4f}")
    
    # 验证转换结果
    expected_conversions = {
        'fitness': 'fitness',
        'metrics/mAP50(B)': 'box_mAP50',
        'metrics/mAP50(M)': 'seg_mAP50',
        'metrics/mAP50-95(B)': 'box_mAP50-95',
        'metrics/mAP50-95(M)': 'seg_mAP50-95',
        'metrics/precision(B)': 'box_precision',
        'metrics/precision(M)': 'seg_precision',
        'metrics/recall(B)': 'box_recall',
        'metrics/recall(M)': 'seg_recall',
        'train/box_loss': 'box_loss',
        'train/seg_loss': 'seg_loss',
        'train/cls_loss': 'cls_loss',
        'train/dfl_loss': 'dfl_loss',
    }
    
    print("\n验证转换结果:")
    all_correct = True
    for yolo_name, expected_name in expected_conversions.items():
        if yolo_name in yolo_metrics:
            if expected_name in converted_metrics:
                original_value = yolo_metrics[yolo_name]
                converted_value = converted_metrics[expected_name]
                if abs(original_value - converted_value) < 1e-6:
                    print(f"  ✓ {yolo_name} → {expected_name}: {converted_value:.4f}")
                else:
                    print(f"  ❌ {yolo_name} → {expected_name}: 值不匹配")
                    all_correct = False
            else:
                print(f"  ❌ {yolo_name} → {expected_name}: 转换失败")
                all_correct = False
    
    print(f"\n转换成功的指标数量: {len(converted_metrics)}")
    print(f"预期转换数量: {len(expected_conversions)}")
    
    if all_correct and len(converted_metrics) == len(expected_conversions):
        print("✅ 所有指标转换正确！")
        return True
    else:
        print("❌ 指标转换存在问题")
        return False

def test_multi_plot_integration():
    """测试与多标签页组件的集成"""
    print("\n测试与多标签页组件的集成")
    print("=" * 50)
    
    try:
        from PySide6.QtWidgets import QApplication
        from multi_plot_widget import MultiPlotWidget
        
        app = QApplication([])
        
        # 创建多标签页组件
        multi_plot = MultiPlotWidget()
        
        # 模拟转换后的指标数据
        converted_metrics = {
            'box_mAP50': 0.0565,
            'seg_mAP50': 0.0391,
            'box_mAP50-95': 0.0417,
            'seg_mAP50-95': 0.0169,
            'box_precision': 0.0506,
            'seg_precision': 0.0341,
            'box_recall': 0.1292,
            'seg_recall': 0.0979,
            'box_loss': 1.234,
            'seg_loss': 0.876,
            'cls_loss': 0.543,
            'dfl_loss': 0.321,
            'fitness': 0.0623,
        }
        
        # 添加数据到多标签页组件
        multi_plot.add_epoch_data(1, converted_metrics)
        
        # 检查数据是否正确存储
        data_stored = False
        stored_metrics = []
        
        for tab_name, plot_widget in multi_plot.plot_widgets.items():
            if plot_widget.epochs and plot_widget.metrics_data:
                data_stored = True
                stored_metrics.extend(list(plot_widget.metrics_data.keys()))
        
        print(f"数据存储状态: {'✓ 成功' if data_stored else '❌ 失败'}")
        print(f"存储的指标: {stored_metrics}")
        
        # 检查各个标签页的数据
        tab_data = {}
        for tab_name, plot_widget in multi_plot.plot_widgets.items():
            tab_metrics = list(plot_widget.metrics_data.keys())
            if tab_metrics:
                tab_data[tab_name] = tab_metrics
                print(f"  {tab_name}: {tab_metrics}")
        
        app.quit()
        
        if data_stored and len(stored_metrics) > 0:
            print("✅ 多标签页组件集成正常！")
            return True
        else:
            print("❌ 多标签页组件集成失败")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 60)
    print("YOLO训练曲线图使用指南")
    print("=" * 60)
    
    print("🎯 现在支持的YOLO指标格式:")
    print("  检测任务:")
    print("    - metrics/precision(B) → box_precision")
    print("    - metrics/recall(B) → box_recall")
    print("    - metrics/mAP50(B) → box_mAP50")
    print("    - metrics/mAP50-95(B) → box_mAP50-95")
    
    print("  分割任务:")
    print("    - metrics/precision(M) → seg_precision")
    print("    - metrics/recall(M) → seg_recall")
    print("    - metrics/mAP50(M) → seg_mAP50")
    print("    - metrics/mAP50-95(M) → seg_mAP50-95")
    
    print("  损失函数:")
    print("    - train/box_loss → box_loss")
    print("    - train/seg_loss → seg_loss")
    print("    - train/cls_loss → cls_loss")
    print("    - train/dfl_loss → dfl_loss")
    
    print("\n📊 标签页分布:")
    print("  1. mAP指标: box_mAP50, box_mAP50-95, seg_mAP50, seg_mAP50-95")
    print("  2. 精度召回: box_precision, box_recall, seg_precision, seg_recall")
    print("  3. 损失函数: box_loss, seg_loss, cls_loss, dfl_loss")
    print("  4. 分类准确率: top1_acc, top5_acc (如果有)")
    
    print("\n🚀 使用方法:")
    print("  1. 启动训练后，指标会自动转换并显示在对应标签页")
    print("  2. 不同任务类型的指标会出现在相应的标签页中")
    print("  3. 每个标签页都支持独立的指标选择和控制")
    print("  4. 训练过程中可以实时切换标签页观察不同指标")

def main():
    """主函数"""
    print("YOLO训练曲线图 - 指标转换功能测试")
    print("=" * 60)
    
    success = True
    
    # 测试指标转换
    if not test_metric_conversion():
        success = False
    
    # 测试多标签页集成
    if not test_multi_plot_integration():
        success = False
    
    # 显示使用指南
    show_usage_guide()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有测试通过！YOLO指标转换功能正常工作。")
        print("\n💡 现在您的训练曲线图应该能正确显示YOLO训练指标了！")
        print("   请重新启动训练来测试实际效果。")
    else:
        print("❌ 测试过程中发现问题，请检查上述错误信息。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
