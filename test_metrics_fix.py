#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试指标处理修复
验证不同类型模型的指标处理是否正常
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from utils import setup_logging, get_model_info

def test_model_metrics():
    """测试不同模型的指标处理"""
    print("测试模型指标处理...")
    
    # 测试不同类型的模型
    test_models = [
        ("models/yolo11n.pt", "检测模型"),
        ("models/yolo11n-seg.pt", "分割模型"),
        ("models/yolo11n-pose.pt", "姿态模型"),
        ("models/yolo11n-cls.pt", "分类模型"),
    ]
    
    for model_path, model_type in test_models:
        print(f"\n测试 {model_type}: {model_path}")
        
        if not os.path.exists(model_path):
            print(f"  ❌ 模型文件不存在，跳过测试")
            continue
            
        try:
            # 分析模型信息
            model_info = get_model_info(model_path)
            
            print(f"  ✓ 模型分析成功")
            print(f"    任务类型: {model_info.get('task', 'unknown')}")
            print(f"    类别数量: {model_info.get('num_classes', 0)}")
            print(f"    架构类型: {model_info.get('architecture', 'unknown')}")
            
            # 测试模型加载
            try:
                from ultralytics import YOLO
                model = YOLO(model_path)
                print(f"  ✓ 模型加载成功")
                
                # 检查模型结构
                if hasattr(model, 'model') and model.model is not None:
                    last_layer = model.model.model[-1]
                    print(f"    检测头类型: {type(last_layer).__name__}")
                    
                    # 检查不同类型的属性
                    if hasattr(last_layer, 'nc'):
                        print(f"    类别数 (nc): {last_layer.nc}")
                    if hasattr(last_layer, 'nm'):
                        print(f"    Mask数 (nm): {last_layer.nm}")
                    if hasattr(last_layer, 'nkpt'):
                        print(f"    关键点数 (nkpt): {last_layer.nkpt}")
                        
            except Exception as e:
                print(f"  ❌ 模型加载失败: {e}")
                
        except Exception as e:
            print(f"  ❌ 模型分析失败: {e}")

def test_metrics_handling():
    """测试指标处理逻辑"""
    print("\n测试指标处理逻辑...")
    
    # 模拟不同类型的指标对象
    class MockDetectMetrics:
        def __init__(self):
            self.results_dict = {
                'precision': 0.85,
                'recall': 0.78,
                'mAP50': 0.82,
                'mAP50-95': 0.65
            }
    
    class MockSegmentMetrics:
        def __init__(self):
            # 分割指标没有items()方法，需要通过属性访问
            self.box = MockBoxMetrics()
            self.seg = MockSegMetrics()
    
    class MockBoxMetrics:
        def __init__(self):
            self.map50 = 0.82
            self.map = 0.65
    
    class MockSegMetrics:
        def __init__(self):
            self.map50 = 0.78
            self.map = 0.61
    
    # 测试检测指标处理
    print("  测试检测指标处理...")
    detect_metrics = MockDetectMetrics()
    if hasattr(detect_metrics, 'results_dict'):
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in detect_metrics.results_dict.items()])
        print(f"    ✓ 检测指标: {metrics_str}")
    
    # 测试分割指标处理
    print("  测试分割指标处理...")
    segment_metrics = MockSegmentMetrics()
    
    metrics_info = []
    if hasattr(segment_metrics, 'box') and hasattr(segment_metrics.box, 'map50'):
        box_metrics = segment_metrics.box
        metrics_info.append(f"box_mAP50: {box_metrics.map50:.4f}")
        metrics_info.append(f"box_mAP50-95: {box_metrics.map:.4f}")
        
    if hasattr(segment_metrics, 'seg') and hasattr(segment_metrics.seg, 'map50'):
        seg_metrics = segment_metrics.seg
        metrics_info.append(f"seg_mAP50: {seg_metrics.map50:.4f}")
        metrics_info.append(f"seg_mAP50-95: {seg_metrics.map:.4f}")
    
    if metrics_info:
        metrics_str = ", ".join(metrics_info)
        print(f"    ✓ 分割指标: {metrics_str}")
    
    print("  ✓ 指标处理逻辑测试通过")

def test_callback_safety():
    """测试回调函数的安全性"""
    print("\n测试回调函数安全性...")
    
    # 模拟可能出现的异常情况
    test_cases = [
        ("空指标对象", None),
        ("无属性对象", object()),
        ("异常对象", type('BadMetrics', (), {'__getattr__': lambda self, name: (_ for _ in ()).throw(Exception("Test error"))})),
    ]
    
    for case_name, metrics_obj in test_cases:
        print(f"  测试 {case_name}...")
        
        try:
            # 模拟回调函数中的指标处理逻辑
            metrics_info = []
            
            if metrics_obj and hasattr(metrics_obj, 'results_dict'):
                metrics_dict = metrics_obj.results_dict
                metrics_info = [f"{k}: {v:.4f}" for k, v in metrics_dict.items() if isinstance(v, (int, float))]
            elif metrics_obj and hasattr(metrics_obj, 'box') and hasattr(metrics_obj.box, 'map50'):
                # 这里会触发异常对象的错误
                box_metrics = metrics_obj.box
                metrics_info.append(f"box_mAP50: {box_metrics.map50:.4f}")
            
            if metrics_info:
                print(f"    ✓ 处理成功: {', '.join(metrics_info)}")
            else:
                print(f"    ✓ 安全跳过")
                
        except Exception as e:
            print(f"    ✓ 异常被捕获: {str(e)[:50]}...")

def main():
    """主测试函数"""
    setup_logging()
    
    print("YOLO指标处理修复测试")
    print("=" * 50)
    
    try:
        # 测试模型指标
        test_model_metrics()
        
        # 测试指标处理逻辑
        test_metrics_handling()
        
        # 测试回调安全性
        test_callback_safety()
        
        print("\n" + "=" * 50)
        print("🎉 指标处理修复测试完成！")
        print("\n主要修复内容:")
        print("✓ 修复了SegmentMetrics对象没有items()方法的问题")
        print("✓ 添加了多种指标获取方式的兼容性处理")
        print("✓ 增强了异常处理，避免训练中断")
        print("✓ 支持检测、分割、姿态等不同任务的指标显示")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
