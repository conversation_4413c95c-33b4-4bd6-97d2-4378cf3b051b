#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多标签页训练曲线图功能
"""

import sys
import time
import random
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import QTimer, Signal, QObject
from multi_plot_widget import MultiPlotWidget
from utils import setup_logging

class MockTrainingSignals(QObject):
    """模拟训练信号"""
    epoch_finished = Signal(int, dict)

class MultiPlotDemo(QMainWindow):
    """多标签页训练曲线图演示"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YOLO多标签页训练曲线图演示")
        self.setGeometry(100, 100, 1400, 900)
        
        # 模拟训练信号
        self.mock_signals = MockTrainingSignals()
        
        # 模拟数据
        self.current_epoch = 0
        self.max_epochs = 100
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始模拟训练")
        self.start_btn.clicked.connect(self.start_simulation)
        
        self.stop_btn = QPushButton("停止模拟")
        self.stop_btn.clicked.connect(self.stop_simulation)
        self.stop_btn.setEnabled(False)
        
        self.reset_btn = QPushButton("重置数据")
        self.reset_btn.clicked.connect(self.reset_data)
        
        self.fast_btn = QPushButton("快速模式")
        self.fast_btn.clicked.connect(self.toggle_fast_mode)
        self.fast_mode = False
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.reset_btn)
        control_layout.addWidget(self.fast_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 多标签页训练曲线图
        self.multi_plot = MultiPlotWidget()
        layout.addWidget(self.multi_plot)
        
        # 连接信号
        self.mock_signals.epoch_finished.connect(self.multi_plot.add_epoch_data)
        
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulate_epoch)
        
    def start_simulation(self):
        """开始模拟训练"""
        self.current_epoch = 0
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 清空数据
        self.multi_plot.clear_all_data()
        
        # 启动定时器
        interval = 100 if self.fast_mode else 500
        self.timer.start(interval)
        
    def stop_simulation(self):
        """停止模拟"""
        self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
    def reset_data(self):
        """重置数据"""
        self.multi_plot.clear_all_data()
        self.current_epoch = 0
        
    def toggle_fast_mode(self):
        """切换快速模式"""
        self.fast_mode = not self.fast_mode
        self.fast_btn.setText("普通模式" if self.fast_mode else "快速模式")
        
        if self.timer.isActive():
            interval = 100 if self.fast_mode else 500
            self.timer.setInterval(interval)
        
    def simulate_epoch(self):
        """模拟一个训练轮次"""
        if self.current_epoch >= self.max_epochs:
            self.stop_simulation()
            return
            
        self.current_epoch += 1
        
        # 生成模拟的训练指标
        metrics = self.generate_comprehensive_metrics(self.current_epoch)
        
        # 发送信号
        self.mock_signals.epoch_finished.emit(self.current_epoch, metrics)
        
    def generate_comprehensive_metrics(self, epoch: int) -> dict:
        """生成全面的模拟训练指标"""
        progress = epoch / self.max_epochs
        
        # 基础趋势函数
        def improving_trend(base, max_val, rate=3):
            return base + (max_val - base) * (1 - 1 / (1 + progress * rate))
        
        def decreasing_trend(base, min_val, rate=3):
            return base - (base - min_val) * (1 - 1 / (1 + progress * rate))
        
        # 添加随机噪声
        def add_noise(value, noise_factor=0.05):
            return value + random.uniform(-noise_factor, noise_factor)
        
        metrics = {}
        
        # mAP指标 (逐渐提高)
        metrics['box_mAP50'] = add_noise(improving_trend(0.2, 0.85))
        metrics['box_mAP50-95'] = add_noise(improving_trend(0.15, 0.65))
        
        # 精度和召回率
        metrics['box_precision'] = add_noise(improving_trend(0.3, 0.9))
        metrics['box_recall'] = add_noise(improving_trend(0.25, 0.85))
        
        # 损失函数 (逐渐降低)
        metrics['train_loss'] = add_noise(decreasing_trend(2.5, 0.3), 0.1)
        metrics['val_loss'] = add_noise(decreasing_trend(2.2, 0.35), 0.1)
        metrics['box_loss'] = add_noise(decreasing_trend(1.5, 0.2), 0.05)
        metrics['cls_loss'] = add_noise(decreasing_trend(1.2, 0.15), 0.05)
        metrics['dfl_loss'] = add_noise(decreasing_trend(0.8, 0.1), 0.03)
        
        # 根据epoch添加不同任务的指标
        if epoch > 20:  # 模拟分割任务
            metrics['seg_mAP50'] = add_noise(improving_trend(0.18, 0.75))
            metrics['seg_mAP50-95'] = add_noise(improving_trend(0.12, 0.55))
            metrics['seg_precision'] = add_noise(improving_trend(0.28, 0.85))
            metrics['seg_recall'] = add_noise(improving_trend(0.22, 0.8))
            metrics['seg_loss'] = add_noise(decreasing_trend(1.3, 0.25), 0.05)
            
        if epoch > 40:  # 模拟姿态估计
            metrics['pose_mAP50'] = add_noise(improving_trend(0.15, 0.7))
            metrics['pose_mAP50-95'] = add_noise(improving_trend(0.1, 0.5))
            
        if epoch > 60:  # 模拟分类任务
            metrics['top1_acc'] = add_noise(improving_trend(0.4, 0.95))
            metrics['top5_acc'] = add_noise(improving_trend(0.6, 0.99))
        
        # 确保所有值都在合理范围内
        for key, value in metrics.items():
            if 'loss' in key:
                metrics[key] = max(0.01, value)  # 损失不能为负或零
            elif 'acc' in key or 'mAP' in key or 'precision' in key or 'recall' in key:
                metrics[key] = max(0.0, min(1.0, value))  # 其他指标在0-1之间
                
        return metrics

def main():
    """主函数"""
    setup_logging()
    
    print("YOLO多标签页训练曲线图功能演示")
    print("=" * 50)
    
    # 检查matplotlib是否可用
    try:
        import matplotlib
        print(f"✓ matplotlib版本: {matplotlib.__version__}")
    except ImportError:
        print("❌ matplotlib不可用，请安装: pip install matplotlib")
        return 1
    
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    
    demo = MultiPlotDemo()
    demo.show()
    
    print("\n多标签页训练曲线图演示已启动！")
    print("功能说明:")
    print("1. 点击'开始模拟训练'开始模拟训练过程")
    print("2. 观察不同标签页中的实时更新曲线")
    print("3. 每个标签页显示不同类型的指标:")
    print("   - mAP指标: 各种mAP值")
    print("   - 精度召回: precision和recall指标")
    print("   - 损失函数: 各种loss值")
    print("   - 分类准确率: top1和top5准确率")
    print("4. 可以在每个标签页中选择显示的指标")
    print("5. 支持快速模式和普通模式")
    print("6. 可保存所有图表或清空数据")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
