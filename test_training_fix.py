#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练功能修复
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtCore import QCoreApplication
from training_worker import TrainingWorker
from utils import setup_logging

def test_training_worker():
    """测试训练工作器"""
    print("测试训练工作器...")
    
    # 创建应用程序（需要用于Qt信号）
    app = QCoreApplication(sys.argv)
    
    # 创建简单的训练配置
    config = {
        'model': 'models/yolo11n.pt',
        'data': 'configs/dataset.yaml',
        'epochs': 2,  # 只训练2轮用于测试
        'batch': 4,
        'imgsz': 640,
        'device': 'cpu',  # 使用CPU避免GPU问题
        'workers': 2,
        'amp': False,  # 关闭混合精度
        'verbose': True
    }
    
    # 检查模型文件是否存在
    if not os.path.exists(config['model']):
        print(f"❌ 模型文件不存在: {config['model']}")
        print("请先下载模型或运行: python test_download.py")
        return False
    
    # 创建训练工作器
    worker = TrainingWorker(config)
    
    # 连接信号
    def on_progress(epoch, total, progress):
        print(f"进度: {epoch}/{total} ({progress:.1f}%)")
    
    def on_epoch_finished(epoch, metrics):
        if metrics:
            metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items() if isinstance(v, (int, float))])
            print(f"轮次 {epoch} 完成 - {metrics_str}")
        else:
            print(f"轮次 {epoch} 完成")
    
    def on_training_finished(results):
        print("✓ 训练完成!")
        print(f"结果: {results}")
        app.quit()
    
    def on_training_error(error):
        print(f"❌ 训练失败: {error}")
        app.quit()
    
    def on_log_message(message):
        print(f"日志: {message}")
    
    worker.progress_updated.connect(on_progress)
    worker.epoch_finished.connect(on_epoch_finished)
    worker.training_finished.connect(on_training_finished)
    worker.training_error.connect(on_training_error)
    worker.log_message.connect(on_log_message)
    
    print("开始测试训练...")
    worker.start()
    
    # 运行事件循环
    app.exec()
    
    return True

def create_test_dataset():
    """创建测试数据集"""
    print("创建测试数据集...")
    
    try:
        from config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 创建测试数据集目录
        dataset_path = Path("test_training_dataset")
        
        for split in ['train', 'val']:
            images_dir = dataset_path / split / 'images'
            labels_dir = dataset_path / split / 'labels'
            images_dir.mkdir(parents=True, exist_ok=True)
            labels_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建几个测试文件
            num_files = 3 if split == 'train' else 2
            for i in range(num_files):
                # 创建空的图像文件
                image_file = images_dir / f"test_{i:03d}.jpg"
                image_file.touch()
                
                # 创建标签文件
                label_file = labels_dir / f"test_{i:03d}.txt"
                with open(label_file, 'w') as f:
                    f.write("0 0.5 0.5 0.3 0.4\n")
        
        # 创建数据集配置
        dataset_config = config_manager.create_dataset_config(
            dataset_path=str(dataset_path),
            train_path="train",
            val_path="val",
            num_classes=1,
            class_names=["test_object"]
        )
        
        print(f"✓ 测试数据集创建完成: {dataset_config}")
        return dataset_config
        
    except Exception as e:
        print(f"❌ 创建测试数据集失败: {e}")
        return None

def cleanup_test_dataset():
    """清理测试数据集"""
    dataset_path = Path("test_training_dataset")
    if dataset_path.exists():
        import shutil
        try:
            shutil.rmtree(dataset_path)
            print(f"✓ 清理测试数据集: {dataset_path}")
        except Exception as e:
            print(f"✗ 清理失败: {e}")

def main():
    """主测试函数"""
    setup_logging()
    
    print("YOLO训练功能修复测试")
    print("=" * 50)
    
    try:
        # 创建测试数据集
        dataset_config = create_test_dataset()
        if not dataset_config:
            print("❌ 无法创建测试数据集，跳过训练测试")
            return 1
        
        # 测试训练工作器
        success = test_training_worker()
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 训练功能修复测试完成！")
        else:
            print("\n" + "=" * 50)
            print("❌ 训练功能测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return 1
        
    finally:
        # 清理测试文件
        cleanup_test_dataset()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
