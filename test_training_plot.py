#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试训练曲线图功能
"""

import sys
import os
import time
import random
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout
from PySide6.QtCore import QTimer, Signal, QObject
from training_plot_widget import TrainingPlotWidget
from utils import setup_logging

class MockTrainingSignals(QObject):
    """模拟训练信号"""
    epoch_finished = Signal(int, dict)

class TrainingPlotDemo(QMainWindow):
    """训练曲线图演示"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YOLO训练曲线图演示")
        self.setGeometry(100, 100, 1200, 800)
        
        # 模拟训练信号
        self.mock_signals = MockTrainingSignals()
        
        # 模拟数据
        self.current_epoch = 0
        self.max_epochs = 50
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始模拟训练")
        self.start_btn.clicked.connect(self.start_simulation)
        
        self.stop_btn = QPushButton("停止模拟")
        self.stop_btn.clicked.connect(self.stop_simulation)
        self.stop_btn.setEnabled(False)
        
        self.reset_btn = QPushButton("重置数据")
        self.reset_btn.clicked.connect(self.reset_data)
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.reset_btn)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        
        # 训练曲线图
        self.training_plot = TrainingPlotWidget()
        layout.addWidget(self.training_plot)
        
        # 连接信号
        self.mock_signals.epoch_finished.connect(self.training_plot.add_epoch_data)
        
    def setup_timer(self):
        """设置定时器"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.simulate_epoch)
        
    def start_simulation(self):
        """开始模拟训练"""
        self.current_epoch = 0
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        # 清空数据
        self.training_plot.clear_data()
        
        # 启动定时器，每500ms模拟一个epoch
        self.timer.start(500)
        
    def stop_simulation(self):
        """停止模拟"""
        self.timer.stop()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
    def reset_data(self):
        """重置数据"""
        self.training_plot.clear_data()
        self.current_epoch = 0
        
    def simulate_epoch(self):
        """模拟一个训练轮次"""
        if self.current_epoch >= self.max_epochs:
            self.stop_simulation()
            return
            
        self.current_epoch += 1
        
        # 生成模拟的训练指标
        metrics = self.generate_mock_metrics(self.current_epoch)
        
        # 发送信号
        self.mock_signals.epoch_finished.emit(self.current_epoch, metrics)
        
    def generate_mock_metrics(self, epoch: int) -> dict:
        """生成模拟的训练指标"""
        # 模拟训练过程中指标的变化趋势
        progress = epoch / self.max_epochs
        
        # 基础趋势：开始时指标较低，逐渐提高，后期趋于稳定
        base_map50 = 0.3 + 0.6 * (1 - 1 / (1 + progress * 5))
        base_map95 = 0.2 + 0.4 * (1 - 1 / (1 + progress * 4))
        
        # 添加随机噪声
        noise_factor = 0.05
        
        metrics = {
            # 检测指标
            'box_mAP50': base_map50 + random.uniform(-noise_factor, noise_factor),
            'box_mAP50-95': base_map95 + random.uniform(-noise_factor, noise_factor),
            'box_precision': 0.4 + 0.5 * (1 - 1 / (1 + progress * 3)) + random.uniform(-noise_factor, noise_factor),
            'box_recall': 0.3 + 0.6 * (1 - 1 / (1 + progress * 4)) + random.uniform(-noise_factor, noise_factor),
        }
        
        # 根据epoch添加不同类型的指标
        if epoch > 10:  # 模拟分割任务
            metrics.update({
                'seg_mAP50': base_map50 * 0.9 + random.uniform(-noise_factor, noise_factor),
                'seg_mAP50-95': base_map95 * 0.85 + random.uniform(-noise_factor, noise_factor),
                'seg_precision': metrics['box_precision'] * 0.9 + random.uniform(-noise_factor, noise_factor),
                'seg_recall': metrics['box_recall'] * 0.9 + random.uniform(-noise_factor, noise_factor),
            })
            
        if epoch > 20:  # 模拟姿态任务
            metrics.update({
                'pose_mAP50': base_map50 * 0.8 + random.uniform(-noise_factor, noise_factor),
                'pose_mAP50-95': base_map95 * 0.75 + random.uniform(-noise_factor, noise_factor),
            })
            
        if epoch > 30:  # 模拟分类任务
            metrics.update({
                'top1_acc': 0.5 + 0.4 * (1 - 1 / (1 + progress * 3)) + random.uniform(-noise_factor, noise_factor),
                'top5_acc': 0.7 + 0.25 * (1 - 1 / (1 + progress * 2)) + random.uniform(-noise_factor, noise_factor),
            })
            
        # 损失指标（递减趋势）
        train_loss_base = 2.0 * (1 / (1 + progress * 3))
        val_loss_base = 1.8 * (1 / (1 + progress * 2.5))
        
        metrics.update({
            'train_loss': train_loss_base + random.uniform(-0.1, 0.1),
            'val_loss': val_loss_base + random.uniform(-0.1, 0.1),
        })
        
        # 确保所有值都在合理范围内
        for key, value in metrics.items():
            if 'loss' in key:
                metrics[key] = max(0.1, value)  # 损失不能为负
            else:
                metrics[key] = max(0.0, min(1.0, value))  # 其他指标在0-1之间
                
        return metrics

def test_plot_widget_standalone(app):
    """测试曲线图组件的独立功能"""
    print("测试训练曲线图组件...")

    # 创建曲线图组件
    plot_widget = TrainingPlotWidget()

    # 添加一些测试数据
    test_data = [
        (1, {'box_mAP50': 0.3, 'box_mAP50-95': 0.2, 'train_loss': 1.5}),
        (2, {'box_mAP50': 0.35, 'box_mAP50-95': 0.22, 'train_loss': 1.3}),
        (3, {'box_mAP50': 0.4, 'box_mAP50-95': 0.25, 'train_loss': 1.1}),
        (4, {'box_mAP50': 0.45, 'box_mAP50-95': 0.28, 'train_loss': 0.9}),
        (5, {'box_mAP50': 0.5, 'box_mAP50-95': 0.3, 'train_loss': 0.8}),
    ]

    for epoch, metrics in test_data:
        plot_widget.add_epoch_data(epoch, metrics)

    print("✓ 测试数据已添加")
    print("✓ 曲线图组件测试完成")

def main():
    """主函数"""
    setup_logging()
    
    print("YOLO训练曲线图功能测试")
    print("=" * 50)
    
    # 检查matplotlib是否可用
    try:
        import matplotlib
        print("✓ matplotlib可用")
    except ImportError:
        print("❌ matplotlib不可用，请安装: pip install matplotlib")
        return 1
    
    # 启动演示应用
    app = QApplication(sys.argv)
    app.setStyle("Fusion")

    # 测试独立组件
    test_plot_widget_standalone(app)
    
    demo = TrainingPlotDemo()
    demo.show()
    
    print("\n演示应用已启动！")
    print("功能说明:")
    print("1. 点击'开始模拟训练'开始模拟训练过程")
    print("2. 观察实时更新的训练曲线")
    print("3. 可以选择显示/隐藏不同的指标")
    print("4. 支持保存图片和清空数据")
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
