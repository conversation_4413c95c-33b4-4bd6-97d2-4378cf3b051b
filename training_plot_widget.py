#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练曲线图组件
实时显示训练过程中的验证指标曲线
"""

import sys
import os
from typing import Dict, List, Any
import logging

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QCheckBox, 
                               QLabel, QPushButton, QComboBox, QSpinBox,
                               QGroupBox, QGridLayout, QScrollArea)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont

try:
    import matplotlib
    matplotlib.use('Qt5Agg')  # 使用Qt后端
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import numpy as np
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("警告: matplotlib未安装，曲线图功能不可用")

logger = logging.getLogger(__name__)

class TrainingPlotWidget(QWidget):
    """训练曲线图组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据存储
        self.epochs = []  # epoch列表
        self.metrics_data = {}  # 指标数据 {metric_name: [values]}
        self.metric_colors = {}  # 指标颜色映射
        
        # 可用的指标类型和颜色
        self.available_metrics = {
            # 检测指标
            'box_mAP50': '#1f77b4',      # 蓝色
            'box_mAP50-95': '#ff7f0e',   # 橙色
            'box_precision': '#2ca02c',   # 绿色
            'box_recall': '#d62728',      # 红色
            
            # 分割指标
            'seg_mAP50': '#9467bd',      # 紫色
            'seg_mAP50-95': '#8c564b',   # 棕色
            'seg_precision': '#e377c2',   # 粉色
            'seg_recall': '#7f7f7f',      # 灰色
            
            # 姿态指标
            'pose_mAP50': '#bcbd22',     # 橄榄色
            'pose_mAP50-95': '#17becf',  # 青色
            
            # 分类指标
            'top1_acc': '#ff9896',       # 浅红色
            'top5_acc': '#98df8a',       # 浅绿色
            
            # 损失指标
            'train_loss': '#ff0000',     # 红色
            'val_loss': '#0000ff',       # 蓝色
        }
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 图表区域
        if MATPLOTLIB_AVAILABLE:
            self.create_plot_area()
            layout.addWidget(self.canvas)
        else:
            no_plot_label = QLabel("matplotlib未安装，无法显示曲线图")
            no_plot_label.setAlignment(Qt.AlignCenter)
            no_plot_label.setStyleSheet("QLabel { color: #dc3545; font-size: 14pt; }")
            layout.addWidget(no_plot_label)
        
    def create_control_panel(self):
        """创建控制面板"""
        group_box = QGroupBox("曲线图控制")
        layout = QVBoxLayout(group_box)
        
        # 第一行：基本控制
        row1 = QHBoxLayout()
        
        # 清空数据按钮
        self.clear_btn = QPushButton("清空数据")
        self.clear_btn.clicked.connect(self.clear_data)
        row1.addWidget(self.clear_btn)
        
        # 保存图片按钮
        self.save_btn = QPushButton("保存图片")
        self.save_btn.clicked.connect(self.save_plot)
        self.save_btn.setEnabled(MATPLOTLIB_AVAILABLE)
        row1.addWidget(self.save_btn)
        
        # 自动缩放
        self.auto_scale_cb = QCheckBox("自动缩放")
        self.auto_scale_cb.setChecked(True)
        row1.addWidget(self.auto_scale_cb)
        
        # 显示网格
        self.show_grid_cb = QCheckBox("显示网格")
        self.show_grid_cb.setChecked(True)
        self.show_grid_cb.toggled.connect(self.update_plot)
        row1.addWidget(self.show_grid_cb)
        
        row1.addStretch()
        layout.addLayout(row1)
        
        # 第二行：指标选择
        row2 = QHBoxLayout()
        row2.addWidget(QLabel("显示指标:"))
        
        # 创建指标复选框（滚动区域）
        scroll_area = QScrollArea()
        scroll_area.setMaximumHeight(100)
        scroll_area.setWidgetResizable(True)
        
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        
        self.metric_checkboxes = {}
        row, col = 0, 0
        for metric_name, color in self.available_metrics.items():
            cb = QCheckBox(metric_name)
            cb.setStyleSheet(f"QCheckBox {{ color: {color}; font-weight: bold; }}")
            cb.toggled.connect(self.update_plot)
            self.metric_checkboxes[metric_name] = cb
            
            scroll_layout.addWidget(cb, row, col)
            col += 1
            if col >= 4:  # 每行4个
                col = 0
                row += 1
        
        scroll_area.setWidget(scroll_widget)
        row2.addWidget(scroll_area)
        layout.addLayout(row2)
        
        return group_box
        
    def create_plot_area(self):
        """创建图表区域"""
        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        
        # 设置图形样式
        self.figure.patch.set_facecolor('white')
        
        # 设置中文字体
        try:
            import matplotlib.pyplot as plt
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
        except:
            pass

        # 创建子图
        self.ax = self.figure.add_subplot(111)
        self.ax.set_xlabel('Epoch', fontsize=12)
        self.ax.set_ylabel('Metric Value', fontsize=12)
        self.ax.set_title('Training Metrics', fontsize=14, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        
        # 初始化空图
        self.ax.text(0.5, 0.5, '等待训练数据...', 
                    transform=self.ax.transAxes, 
                    ha='center', va='center',
                    fontsize=16, alpha=0.5)
        
        self.canvas.draw()
        
    def add_epoch_data(self, epoch: int, metrics: Dict[str, Any]):
        """添加一个epoch的数据"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        # 添加epoch
        if epoch not in self.epochs:
            self.epochs.append(epoch)
            
        # 添加指标数据
        for metric_name, value in metrics.items():
            if isinstance(value, (int, float)) and not np.isnan(value):
                if metric_name not in self.metrics_data:
                    self.metrics_data[metric_name] = []
                    # 如果是新指标且在可用列表中，自动勾选
                    if metric_name in self.metric_checkboxes:
                        self.metric_checkboxes[metric_name].setChecked(True)
                
                # 确保数据长度一致
                while len(self.metrics_data[metric_name]) < len(self.epochs) - 1:
                    self.metrics_data[metric_name].append(None)
                    
                self.metrics_data[metric_name].append(value)
        
        # 更新图表
        self.update_plot()
        
    def update_plot(self):
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE or not self.epochs:
            return
            
        self.ax.clear()
        
        # 设置基本属性
        self.ax.set_xlabel('Epoch', fontsize=12)
        self.ax.set_ylabel('Metric Value', fontsize=12)
        self.ax.set_title('Training Metrics', fontsize=14, fontweight='bold')
        
        # 显示网格
        if self.show_grid_cb.isChecked():
            self.ax.grid(True, alpha=0.3)
        
        # 绘制选中的指标
        plotted_any = False
        for metric_name, checkbox in self.metric_checkboxes.items():
            if checkbox.isChecked() and metric_name in self.metrics_data:
                values = self.metrics_data[metric_name]
                if values and any(v is not None for v in values):
                    # 过滤None值
                    epochs_filtered = []
                    values_filtered = []
                    for i, (epoch, value) in enumerate(zip(self.epochs, values)):
                        if value is not None:
                            epochs_filtered.append(epoch)
                            values_filtered.append(value)
                    
                    if epochs_filtered and values_filtered:
                        color = self.available_metrics.get(metric_name, '#000000')
                        self.ax.plot(epochs_filtered, values_filtered, 
                                   label=metric_name, color=color, 
                                   linewidth=2, marker='o', markersize=4)
                        plotted_any = True
        
        if plotted_any:
            # 添加图例
            self.ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            
            # 自动缩放
            if self.auto_scale_cb.isChecked():
                self.ax.relim()
                self.ax.autoscale_view()
        else:
            # 没有数据时显示提示
            self.ax.text(0.5, 0.5, '请选择要显示的指标', 
                        transform=self.ax.transAxes, 
                        ha='center', va='center',
                        fontsize=16, alpha=0.5)
        
        # 调整布局
        self.figure.tight_layout()
        self.canvas.draw()
        
    def clear_data(self):
        """清空所有数据"""
        self.epochs.clear()
        self.metrics_data.clear()
        
        if MATPLOTLIB_AVAILABLE:
            self.ax.clear()
            self.ax.set_xlabel('Epoch', fontsize=12)
            self.ax.set_ylabel('Metric Value', fontsize=12)
            self.ax.set_title('Training Metrics', fontsize=14, fontweight='bold')
            self.ax.text(0.5, 0.5, '数据已清空', 
                        transform=self.ax.transAxes, 
                        ha='center', va='center',
                        fontsize=16, alpha=0.5)
            self.canvas.draw()
        
        logger.info("训练曲线数据已清空")
        
    def save_plot(self):
        """保存图片"""
        if not MATPLOTLIB_AVAILABLE:
            return
            
        from PySide6.QtWidgets import QFileDialog
        
        filename, _ = QFileDialog.getSaveFileName(
            self, "保存训练曲线图", 
            "training_metrics.png",
            "PNG files (*.png);;PDF files (*.pdf);;SVG files (*.svg)"
        )
        
        if filename:
            try:
                self.figure.savefig(filename, dpi=300, bbox_inches='tight')
                logger.info(f"训练曲线图已保存到: {filename}")
            except Exception as e:
                logger.error(f"保存图片失败: {e}")
                
    def get_current_metrics(self):
        """获取当前的指标数据"""
        return {
            'epochs': self.epochs.copy(),
            'metrics_data': {k: v.copy() for k, v in self.metrics_data.items()}
        }
        
    def load_metrics_data(self, data: Dict):
        """加载指标数据"""
        if 'epochs' in data:
            self.epochs = data['epochs']
        if 'metrics_data' in data:
            self.metrics_data = data['metrics_data']
            
        self.update_plot()
        
    def set_metric_visibility(self, metric_name: str, visible: bool):
        """设置指标可见性"""
        if metric_name in self.metric_checkboxes:
            self.metric_checkboxes[metric_name].setChecked(visible)
            
    def get_available_metrics(self):
        """获取可用的指标列表"""
        return list(self.available_metrics.keys())
