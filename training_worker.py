#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练工作器
负责在后台线程中执行YOLO模型训练
"""

import os
import sys
import logging
import traceback
from typing import Dict, Any, Optional
from pathlib import Path

from PySide6.QtCore import QThread, Signal
from ultralytics import YOLO
from ultralytics.utils.callbacks import default_callbacks

logger = logging.getLogger(__name__)


class TrainingWorker(QThread):
    """训练工作器线程"""
    
    # 信号定义
    progress_updated = Signal(int, int, float)  # 当前轮次, 总轮次, 进度百分比
    epoch_finished = Signal(int, dict)          # 轮次完成, 指标字典
    training_finished = Signal(dict)            # 训练完成, 结果字典
    training_error = Signal(str)                # 训练错误, 错误信息
    log_message = Signal(str)                   # 日志消息
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        self.config = config
        self.model = None
        self.is_stopped = False
        self.current_epoch = 0
        self.total_epochs = config.get('epochs', 100)
        
    def run(self):
        """执行训练"""
        try:
            self.log_message.emit("初始化训练环境...")

            # 创建模型
            self.create_model()

            # 设置回调函数
            self.setup_callbacks()

            # 开始训练
            self.log_message.emit("开始训练...")
            results = self.model.train(**self.get_train_args())
            
            if not self.is_stopped:
                # 训练完成
                self.training_finished.emit({
                    'best_model_path': str(results.save_dir / 'weights' / 'best.pt'),
                    'last_model_path': str(results.save_dir / 'weights' / 'last.pt'),
                    'results_dir': str(results.save_dir),
                    'metrics': results.results_dict if hasattr(results, 'results_dict') else {}
                })
                
        except Exception as e:
            error_msg = f"训练失败: {str(e)}\n{traceback.format_exc()}"
            logger.error(error_msg)
            self.training_error.emit(str(e))
            
    def create_model(self):
        """创建YOLO模型"""
        model_path = self.config.get('model', 'yolo11n.pt')
        
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            # 如果是预训练模型名称，尝试自动下载
            if model_path.endswith('.pt') and not os.path.dirname(model_path):
                self.log_message.emit(f"模型文件不存在，尝试自动下载: {model_path}")
                
        self.log_message.emit(f"加载模型: {model_path}")
        self.model = YOLO(model_path)
        
    def setup_callbacks(self):
        """设置训练回调函数"""
        def on_train_epoch_end(trainer):
            """训练轮次结束回调"""
            if self.is_stopped:
                return
                
            self.current_epoch = trainer.epoch + 1
            progress = (self.current_epoch / self.total_epochs) * 100
            
            # 获取训练指标
            metrics = {}
            try:
                if hasattr(trainer, 'metrics') and trainer.metrics:
                    # 尝试从trainer.metrics获取
                    if hasattr(trainer.metrics, 'results_dict'):
                        metrics = trainer.metrics.results_dict
                    elif hasattr(trainer.metrics, 'box') and hasattr(trainer.metrics.box, 'map50'):
                        # 构建指标字典
                        box_metrics = trainer.metrics.box
                        if hasattr(box_metrics, 'map50'):
                            metrics['box_mAP50'] = box_metrics.map50
                        if hasattr(box_metrics, 'map'):
                            metrics['box_mAP50-95'] = box_metrics.map

                        # 如果有分割指标
                        if hasattr(trainer.metrics, 'seg') and hasattr(trainer.metrics.seg, 'map50'):
                            seg_metrics = trainer.metrics.seg
                            if hasattr(seg_metrics, 'map50'):
                                metrics['seg_mAP50'] = seg_metrics.map50
                            if hasattr(seg_metrics, 'map'):
                                metrics['seg_mAP50-95'] = seg_metrics.map

                elif hasattr(trainer, 'validator') and hasattr(trainer.validator, 'metrics'):
                    # 从validator获取指标
                    validator_metrics = trainer.validator.metrics
                    if hasattr(validator_metrics, 'results_dict'):
                        metrics = validator_metrics.results_dict

            except Exception as e:
                logger.warning(f"获取训练指标失败: {e}")
                metrics = {}

            # 发送信号
            self.progress_updated.emit(self.current_epoch, self.total_epochs, progress)
            self.epoch_finished.emit(self.current_epoch, metrics)
            
        def on_train_start(trainer):
            """训练开始回调"""
            self.log_message.emit(f"开始训练 {self.total_epochs} 轮次")

        def on_train_end(trainer):
            """训练结束回调"""
            if not self.is_stopped:
                self.log_message.emit("训练完成")

        def on_val_end(validator):
            """验证结束回调"""
            if hasattr(validator, 'metrics') and validator.metrics:
                try:
                    # 尝试获取指标信息
                    metrics_info = []

                    # 检查是否有results_dict属性
                    if hasattr(validator.metrics, 'results_dict'):
                        metrics_dict = validator.metrics.results_dict
                        metrics_info = [f"{k}: {v:.4f}" for k, v in metrics_dict.items() if isinstance(v, (int, float))]

                    # 如果没有results_dict，尝试获取其他属性
                    elif hasattr(validator.metrics, 'box') and hasattr(validator.metrics.box, 'map50'):
                        box_metrics = validator.metrics.box
                        if hasattr(box_metrics, 'map50'):
                            metrics_info.append(f"box_mAP50: {box_metrics.map50:.4f}")
                        if hasattr(box_metrics, 'map'):
                            metrics_info.append(f"box_mAP50-95: {box_metrics.map:.4f}")

                        # 如果是分割任务，添加分割指标
                        if hasattr(validator.metrics, 'seg') and hasattr(validator.metrics.seg, 'map50'):
                            seg_metrics = validator.metrics.seg
                            if hasattr(seg_metrics, 'map50'):
                                metrics_info.append(f"seg_mAP50: {seg_metrics.map50:.4f}")
                            if hasattr(seg_metrics, 'map'):
                                metrics_info.append(f"seg_mAP50-95: {seg_metrics.map:.4f}")

                    # 输出指标信息
                    if metrics_info:
                        metrics_str = ", ".join(metrics_info)
                        self.log_message.emit(f"验证指标: {metrics_str}")
                    else:
                        self.log_message.emit("验证完成")

                except Exception as e:
                    # 如果获取指标失败，只输出简单信息
                    self.log_message.emit(f"验证完成 (指标获取失败: {str(e)})")
        
        # 添加回调函数
        if self.model:
            self.model.add_callback('on_train_epoch_end', on_train_epoch_end)
            self.model.add_callback('on_train_start', on_train_start)
            self.model.add_callback('on_train_end', on_train_end)
            self.model.add_callback('on_val_end', on_val_end)
            
    def get_train_args(self) -> Dict[str, Any]:
        """获取训练参数"""
        args = {}
        
        # 基础参数
        basic_params = [
            'data', 'epochs', 'batch', 'imgsz', 'device', 'workers',
            'optimizer', 'lr0', 'lrf', 'momentum', 'weight_decay',
            'warmup_epochs', 'patience', 'save_period', 'amp', 'cos_lr'
        ]
        
        for param in basic_params:
            if param in self.config:
                args[param] = self.config[param]
                
        # 数据增强参数
        augment_params = [
            'hsv_h', 'hsv_s', 'hsv_v', 'degrees', 'translate', 'scale',
            'shear', 'perspective', 'fliplr', 'flipud', 'mosaic', 'mixup'
        ]
        
        for param in augment_params:
            if param in self.config:
                args[param] = self.config[param]
                
        # 其他参数
        args['save'] = True
        args['plots'] = True
        args['val'] = True
        args['verbose'] = True
        
        # 项目和名称
        args['project'] = 'runs/train'
        args['name'] = 'exp'
        args['exist_ok'] = True
        
        return args
        
    def stop(self):
        """停止训练"""
        self.is_stopped = True
        self.log_message.emit("正在停止训练...")

        # 如果模型存在，尝试停止训练
        if self.model and hasattr(self.model, 'trainer'):
            try:
                self.model.trainer.stop_training = True
            except:
                pass

        # 等待线程结束
        self.quit()
        self.wait(5000)  # 等待5秒
