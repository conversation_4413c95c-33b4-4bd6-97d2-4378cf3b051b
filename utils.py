#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数
提供各种辅助功能
"""

import os
import sys
import logging
import platform
from pathlib import Path
from typing import Optional, List, Dict, Any


def setup_logging(level: int = logging.INFO, log_file: Optional[str] = None):
    """设置日志配置"""
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)


def create_models_dir():
    """创建models目录"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    return models_dir


def create_configs_dir():
    """创建configs目录"""
    configs_dir = Path("configs")
    configs_dir.mkdir(exist_ok=True)
    return configs_dir


def create_runs_dir():
    """创建runs目录"""
    runs_dir = Path("runs")
    runs_dir.mkdir(exist_ok=True)
    return runs_dir


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    info = {
        'platform': platform.platform(),
        'system': platform.system(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version(),
        'python_executable': sys.executable,
    }
    
    # 检查CUDA可用性
    try:
        import torch
        info['torch_version'] = torch.__version__
        info['cuda_available'] = torch.cuda.is_available()
        if torch.cuda.is_available():
            info['cuda_version'] = torch.version.cuda
            info['cuda_device_count'] = torch.cuda.device_count()
            info['cuda_device_names'] = [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]
    except ImportError:
        info['torch_version'] = 'Not installed'
        info['cuda_available'] = False
    
    # 检查ultralytics版本
    try:
        import ultralytics
        info['ultralytics_version'] = ultralytics.__version__
    except ImportError:
        info['ultralytics_version'] = 'Not installed'
    
    return info


def check_dependencies() -> List[str]:
    """检查依赖项"""
    missing_deps = []
    
    # 必需的依赖项
    required_deps = [
        'torch',
        'torchvision', 
        'ultralytics',
        'PySide6',
        'numpy',
        'opencv-python',
        'pillow',
        'pyyaml',
        'requests'
    ]
    
    for dep in required_deps:
        try:
            __import__(dep.replace('-', '_'))
        except ImportError:
            missing_deps.append(dep)
    
    return missing_deps


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def format_time(seconds: float) -> str:
    """格式化时间"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{int(minutes)}m {seconds:.1f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{int(hours)}h {int(minutes)}m {seconds:.1f}s"


def validate_dataset_structure(dataset_path: str) -> Dict[str, Any]:
    """验证数据集结构"""
    dataset_path = Path(dataset_path)
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    if not dataset_path.exists():
        result['valid'] = False
        result['errors'].append(f"数据集路径不存在: {dataset_path}")
        return result
    
    # 检查基本结构
    train_dir = dataset_path / "train"
    val_dir = dataset_path / "val"
    test_dir = dataset_path / "test"
    
    if not train_dir.exists():
        result['warnings'].append("训练集目录不存在: train/")
    
    if not val_dir.exists():
        result['warnings'].append("验证集目录不存在: val/")
    
    if not test_dir.exists():
        result['warnings'].append("测试集目录不存在: test/")
    
    # 检查图像和标签目录
    for split_name, split_dir in [("train", train_dir), ("val", val_dir), ("test", test_dir)]:
        if split_dir.exists():
            images_dir = split_dir / "images"
            labels_dir = split_dir / "labels"
            
            if not images_dir.exists():
                result['warnings'].append(f"{split_name}/images/ 目录不存在")
            else:
                # 统计图像文件
                image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpeg"))
                result['info'][f'{split_name}_images'] = len(image_files)
            
            if not labels_dir.exists():
                result['warnings'].append(f"{split_name}/labels/ 目录不存在")
            else:
                # 统计标签文件
                label_files = list(labels_dir.glob("*.txt"))
                result['info'][f'{split_name}_labels'] = len(label_files)
    
    return result


def get_available_devices() -> List[str]:
    """获取可用的设备列表"""
    devices = ['cpu']
    
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                devices.append(str(i))
                
        # 检查MPS（Apple Silicon）
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            devices.append('mps')
            
    except ImportError:
        pass
    
    return devices


def estimate_training_time(epochs: int, batch_size: int, dataset_size: int, device: str = 'cpu') -> float:
    """估算训练时间（秒）"""
    # 基础时间估算（非常粗略）
    base_time_per_image = {
        'cpu': 0.5,      # CPU每张图片0.5秒
        '0': 0.05,       # GPU每张图片0.05秒
        'mps': 0.1,      # MPS每张图片0.1秒
    }
    
    time_per_image = base_time_per_image.get(device, base_time_per_image['cpu'])
    
    # 计算每个epoch的时间
    time_per_epoch = (dataset_size / batch_size) * time_per_image * batch_size
    
    # 总训练时间
    total_time = time_per_epoch * epochs
    
    return total_time


def create_sample_dataset_config():
    """创建示例数据集配置"""
    config = {
        'path': './dataset',
        'train': 'train/images',
        'val': 'val/images', 
        'test': 'test/images',
        'nc': 2,
        'names': ['class1', 'class2']
    }
    
    config_path = Path("configs/sample_dataset.yaml")
    config_path.parent.mkdir(exist_ok=True)
    
    import yaml
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    return str(config_path)


def cleanup_temp_files():
    """清理临时文件"""
    temp_dirs = [
        Path("temp"),
        Path("tmp"),
        Path(".cache"),
    ]
    
    for temp_dir in temp_dirs:
        if temp_dir.exists() and temp_dir.is_dir():
            try:
                import shutil
                shutil.rmtree(temp_dir)
                logging.info(f"已清理临时目录: {temp_dir}")
            except Exception as e:
                logging.warning(f"清理临时目录失败 {temp_dir}: {e}")


def get_model_info(model_path: str) -> Dict[str, Any]:
    """获取模型信息"""
    model_path = Path(model_path)
    info = {
        'exists': model_path.exists(),
        'size': 0,
        'size_formatted': '0B',
        'type': 'unknown',
        'task': 'unknown',
        'num_classes': 0,
        'model_name': '',
        'architecture': ''
    }

    if model_path.exists():
        info['size'] = model_path.stat().st_size
        info['size_formatted'] = format_size(info['size'])
        info['model_name'] = model_path.stem

        if model_path.suffix == '.pt':
            info['type'] = 'PyTorch'
            # 尝试分析PyTorch模型
            task_info = analyze_model_task(str(model_path))
            info.update(task_info)
        elif model_path.suffix == '.yaml':
            info['type'] = 'YAML Config'
        elif model_path.suffix == '.onnx':
            info['type'] = 'ONNX'

    return info


def analyze_model_task(model_path: str) -> Dict[str, Any]:
    """分析模型任务类型"""
    task_info = {
        'task': 'unknown',
        'num_classes': 0,
        'architecture': '',
        'head_info': {}
    }

    try:
        import torch
        from ultralytics import YOLO

        # 加载模型
        model = YOLO(model_path)

        # 从模型名称推断任务类型
        model_name = Path(model_path).stem.lower()
        if 'seg' in model_name:
            task_info['task'] = 'segment'
        elif 'pose' in model_name:
            task_info['task'] = 'pose'
        elif 'cls' in model_name:
            task_info['task'] = 'classify'
        elif 'obb' in model_name:
            task_info['task'] = 'obb'
        else:
            task_info['task'] = 'detect'

        # 尝试获取模型详细信息
        if hasattr(model, 'model') and model.model is not None:
            # 获取模型架构信息
            if hasattr(model.model, 'model'):
                model_layers = model.model.model
                task_info['architecture'] = str(type(model_layers[-1]).__name__)

                # 分析检测头
                head_analysis = analyze_detection_head(model.model)
                task_info.update(head_analysis)

        # 尝试从模型配置获取类别数
        if hasattr(model, 'names') and model.names:
            task_info['num_classes'] = len(model.names)

        logging.info(f"模型任务分析完成: {task_info}")

    except Exception as e:
        logging.warning(f"模型任务分析失败: {e}")
        # 从文件名推断
        model_name = Path(model_path).stem.lower()
        if 'seg' in model_name:
            task_info['task'] = 'segment'
        elif 'pose' in model_name:
            task_info['task'] = 'pose'
        elif 'cls' in model_name:
            task_info['task'] = 'classify'
        elif 'obb' in model_name:
            task_info['task'] = 'obb'
        else:
            task_info['task'] = 'detect'

    return task_info


def analyze_detection_head(model) -> Dict[str, Any]:
    """分析检测头结构"""
    head_info = {
        'task': 'detect',
        'num_classes': 80,
        'head_type': 'unknown'
    }

    try:
        # 获取最后一层（通常是检测头）
        if hasattr(model, 'model') and len(model.model) > 0:
            last_layer = model.model[-1]
            head_info['head_type'] = type(last_layer).__name__

            # 分析不同类型的检测头
            if hasattr(last_layer, 'nc'):  # 类别数
                head_info['num_classes'] = last_layer.nc

            if hasattr(last_layer, 'nm'):  # mask数量（分割）
                head_info['task'] = 'segment'
                head_info['num_masks'] = last_layer.nm

            if hasattr(last_layer, 'nkpt'):  # 关键点数量（姿态）
                head_info['task'] = 'pose'
                head_info['num_keypoints'] = last_layer.nkpt

            # 检查输出维度来推断任务
            if hasattr(last_layer, 'cv2') and hasattr(last_layer.cv2, 'weight'):
                output_channels = last_layer.cv2.weight.shape[0]
                if output_channels == head_info['num_classes']:
                    head_info['task'] = 'classify'
                elif output_channels > head_info['num_classes'] + 4:
                    # 可能是分割或姿态估计
                    extra_channels = output_channels - head_info['num_classes'] - 4
                    if extra_channels == 32:  # 通常分割有32个mask原型
                        head_info['task'] = 'segment'
                    elif extra_channels % 3 == 0:  # 姿态估计，每个关键点3个值
                        head_info['task'] = 'pose'
                        head_info['num_keypoints'] = extra_channels // 3

    except Exception as e:
        logging.warning(f"检测头分析失败: {e}")

    return head_info


def auto_detect_dataset(dataset_path: str) -> Dict[str, Any]:
    """自动检测数据集结构和任务类型"""
    dataset_path = Path(dataset_path)
    dataset_info = {
        'valid': False,
        'task': 'unknown',
        'splits': [],
        'num_classes': 0,
        'class_names': [],
        'total_images': 0,
        'total_labels': 0,
        'structure': {},
        'errors': [],
        'warnings': []
    }

    if not dataset_path.exists():
        dataset_info['errors'].append(f"数据集路径不存在: {dataset_path}")
        return dataset_info

    # 检测数据集结构
    splits = ['train', 'val', 'test']
    found_splits = []

    for split in splits:
        split_dir = dataset_path / split
        if split_dir.exists():
            found_splits.append(split)

            # 检查images和labels目录
            images_dir = split_dir / 'images'
            labels_dir = split_dir / 'labels'

            if images_dir.exists():
                image_files = list(images_dir.glob('*.jpg')) + list(images_dir.glob('*.png')) + list(images_dir.glob('*.jpeg'))
                dataset_info['structure'][f'{split}_images'] = len(image_files)
                dataset_info['total_images'] += len(image_files)

                if labels_dir.exists():
                    label_files = list(labels_dir.glob('*.txt'))
                    dataset_info['structure'][f'{split}_labels'] = len(label_files)
                    dataset_info['total_labels'] += len(label_files)

                    # 分析标签文件来推断任务类型
                    if label_files and dataset_info['task'] == 'unknown':
                        task_analysis = analyze_label_format(label_files[0])
                        dataset_info.update(task_analysis)

    dataset_info['splits'] = found_splits
    dataset_info['valid'] = len(found_splits) >= 2  # 至少需要train和val

    # 尝试查找类别信息
    class_info = find_class_names(dataset_path)
    dataset_info.update(class_info)

    # 检查数据集完整性
    integrity_check = check_dataset_integrity(dataset_path, found_splits)
    dataset_info['warnings'].extend(integrity_check)

    return dataset_info


def analyze_label_format(label_file: Path) -> Dict[str, Any]:
    """分析标签文件格式来推断任务类型"""
    task_info = {
        'task': 'detect',
        'label_format': 'yolo',
        'avg_objects_per_image': 0
    }

    try:
        with open(label_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        if not lines:
            return task_info

        # 分析第一行来推断格式
        first_line = lines[0].strip().split()

        if len(first_line) >= 5:
            # 标准YOLO格式: class_id x_center y_center width height
            task_info['task'] = 'detect'
            task_info['label_format'] = 'yolo_detect'

            # 检查是否有额外的坐标点（可能是分割或姿态）
            if len(first_line) > 5:
                extra_coords = len(first_line) - 5
                if extra_coords % 2 == 0:  # 成对的坐标
                    if extra_coords >= 6:  # 至少3个点
                        task_info['task'] = 'segment'
                        task_info['label_format'] = 'yolo_segment'
                    elif extra_coords == 34:  # 17个关键点 * 2
                        task_info['task'] = 'pose'
                        task_info['label_format'] = 'yolo_pose'

        elif len(first_line) == 1:
            # 可能是分类任务
            task_info['task'] = 'classify'
            task_info['label_format'] = 'classification'

        task_info['avg_objects_per_image'] = len(lines)

    except Exception as e:
        logging.warning(f"标签格式分析失败: {e}")

    return task_info


def find_class_names(dataset_path: Path) -> Dict[str, Any]:
    """查找类别名称"""
    class_info = {
        'num_classes': 0,
        'class_names': [],
        'class_source': 'unknown'
    }

    # 查找可能的类别文件
    possible_files = [
        dataset_path / 'classes.txt',
        dataset_path / 'names.txt',
        dataset_path / 'labels.txt',
        dataset_path / 'data.yaml',
        dataset_path / 'dataset.yaml'
    ]

    for file_path in possible_files:
        if file_path.exists():
            try:
                if file_path.suffix == '.yaml':
                    import yaml
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = yaml.safe_load(f)

                    if 'names' in data:
                        if isinstance(data['names'], list):
                            class_info['class_names'] = data['names']
                        elif isinstance(data['names'], dict):
                            class_info['class_names'] = list(data['names'].values())
                        class_info['class_source'] = str(file_path.name)
                        break

                    if 'nc' in data:
                        class_info['num_classes'] = data['nc']

                else:
                    # 文本文件，每行一个类别
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    class_info['class_names'] = [line.strip() for line in lines if line.strip()]
                    class_info['class_source'] = str(file_path.name)
                    break

            except Exception as e:
                logging.warning(f"读取类别文件失败 {file_path}: {e}")

    # 如果没有找到类别文件，尝试从标签文件推断
    if not class_info['class_names']:
        class_info.update(infer_classes_from_labels(dataset_path))

    class_info['num_classes'] = len(class_info['class_names'])
    return class_info


def infer_classes_from_labels(dataset_path: Path) -> Dict[str, Any]:
    """从标签文件推断类别"""
    class_info = {
        'class_names': [],
        'class_source': 'inferred_from_labels'
    }

    class_ids = set()

    # 扫描所有标签文件
    for split in ['train', 'val', 'test']:
        labels_dir = dataset_path / split / 'labels'
        if labels_dir.exists():
            for label_file in labels_dir.glob('*.txt'):
                try:
                    with open(label_file, 'r', encoding='utf-8') as f:
                        for line in f:
                            parts = line.strip().split()
                            if parts:
                                class_id = int(parts[0])
                                class_ids.add(class_id)
                except:
                    continue

    if class_ids:
        max_class_id = max(class_ids)
        class_info['class_names'] = [f'class_{i}' for i in range(max_class_id + 1)]

    return class_info


def check_dataset_integrity(dataset_path: Path, splits: list) -> list:
    """检查数据集完整性"""
    warnings = []

    for split in splits:
        images_dir = dataset_path / split / 'images'
        labels_dir = dataset_path / split / 'labels'

        if not images_dir.exists():
            warnings.append(f"{split}/images 目录不存在")
            continue

        if not labels_dir.exists():
            warnings.append(f"{split}/labels 目录不存在")
            continue

        # 检查图像和标签文件数量匹配
        image_files = set(f.stem for f in images_dir.glob('*.jpg')) | \
                     set(f.stem for f in images_dir.glob('*.png')) | \
                     set(f.stem for f in images_dir.glob('*.jpeg'))

        label_files = set(f.stem for f in labels_dir.glob('*.txt'))

        missing_labels = image_files - label_files
        missing_images = label_files - image_files

        if missing_labels:
            warnings.append(f"{split}: {len(missing_labels)} 个图像缺少标签文件")

        if missing_images:
            warnings.append(f"{split}: {len(missing_images)} 个标签文件缺少对应图像")

    return warnings
