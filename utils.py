#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数
提供各种辅助功能
"""

import os
import sys
import logging
import platform
from pathlib import Path
from typing import Optional, List, Dict, Any


def setup_logging(level: int = logging.INFO, log_file: Optional[str] = None):
    """设置日志配置"""
    # 创建日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 设置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('PIL').setLevel(logging.WARNING)


def create_models_dir():
    """创建models目录"""
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    return models_dir


def create_configs_dir():
    """创建configs目录"""
    configs_dir = Path("configs")
    configs_dir.mkdir(exist_ok=True)
    return configs_dir


def create_runs_dir():
    """创建runs目录"""
    runs_dir = Path("runs")
    runs_dir.mkdir(exist_ok=True)
    return runs_dir


def get_system_info() -> Dict[str, Any]:
    """获取系统信息"""
    info = {
        'platform': platform.platform(),
        'system': platform.system(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version(),
        'python_executable': sys.executable,
    }
    
    # 检查CUDA可用性
    try:
        import torch
        info['torch_version'] = torch.__version__
        info['cuda_available'] = torch.cuda.is_available()
        if torch.cuda.is_available():
            info['cuda_version'] = torch.version.cuda
            info['cuda_device_count'] = torch.cuda.device_count()
            info['cuda_device_names'] = [torch.cuda.get_device_name(i) for i in range(torch.cuda.device_count())]
    except ImportError:
        info['torch_version'] = 'Not installed'
        info['cuda_available'] = False
    
    # 检查ultralytics版本
    try:
        import ultralytics
        info['ultralytics_version'] = ultralytics.__version__
    except ImportError:
        info['ultralytics_version'] = 'Not installed'
    
    return info


def check_dependencies() -> List[str]:
    """检查依赖项"""
    missing_deps = []
    
    # 必需的依赖项
    required_deps = [
        'torch',
        'torchvision', 
        'ultralytics',
        'PySide6',
        'numpy',
        'opencv-python',
        'pillow',
        'pyyaml',
        'requests'
    ]
    
    for dep in required_deps:
        try:
            __import__(dep.replace('-', '_'))
        except ImportError:
            missing_deps.append(dep)
    
    return missing_deps


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def format_time(seconds: float) -> str:
    """格式化时间"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds // 60
        seconds = seconds % 60
        return f"{int(minutes)}m {seconds:.1f}s"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        seconds = seconds % 60
        return f"{int(hours)}h {int(minutes)}m {seconds:.1f}s"


def validate_dataset_structure(dataset_path: str) -> Dict[str, Any]:
    """验证数据集结构"""
    dataset_path = Path(dataset_path)
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    if not dataset_path.exists():
        result['valid'] = False
        result['errors'].append(f"数据集路径不存在: {dataset_path}")
        return result
    
    # 检查基本结构
    train_dir = dataset_path / "train"
    val_dir = dataset_path / "val"
    test_dir = dataset_path / "test"
    
    if not train_dir.exists():
        result['warnings'].append("训练集目录不存在: train/")
    
    if not val_dir.exists():
        result['warnings'].append("验证集目录不存在: val/")
    
    if not test_dir.exists():
        result['warnings'].append("测试集目录不存在: test/")
    
    # 检查图像和标签目录
    for split_name, split_dir in [("train", train_dir), ("val", val_dir), ("test", test_dir)]:
        if split_dir.exists():
            images_dir = split_dir / "images"
            labels_dir = split_dir / "labels"
            
            if not images_dir.exists():
                result['warnings'].append(f"{split_name}/images/ 目录不存在")
            else:
                # 统计图像文件
                image_files = list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpeg"))
                result['info'][f'{split_name}_images'] = len(image_files)
            
            if not labels_dir.exists():
                result['warnings'].append(f"{split_name}/labels/ 目录不存在")
            else:
                # 统计标签文件
                label_files = list(labels_dir.glob("*.txt"))
                result['info'][f'{split_name}_labels'] = len(label_files)
    
    return result


def get_available_devices() -> List[str]:
    """获取可用的设备列表"""
    devices = ['cpu']
    
    try:
        import torch
        if torch.cuda.is_available():
            for i in range(torch.cuda.device_count()):
                devices.append(str(i))
                
        # 检查MPS（Apple Silicon）
        if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            devices.append('mps')
            
    except ImportError:
        pass
    
    return devices


def estimate_training_time(epochs: int, batch_size: int, dataset_size: int, device: str = 'cpu') -> float:
    """估算训练时间（秒）"""
    # 基础时间估算（非常粗略）
    base_time_per_image = {
        'cpu': 0.5,      # CPU每张图片0.5秒
        '0': 0.05,       # GPU每张图片0.05秒
        'mps': 0.1,      # MPS每张图片0.1秒
    }
    
    time_per_image = base_time_per_image.get(device, base_time_per_image['cpu'])
    
    # 计算每个epoch的时间
    time_per_epoch = (dataset_size / batch_size) * time_per_image * batch_size
    
    # 总训练时间
    total_time = time_per_epoch * epochs
    
    return total_time


def create_sample_dataset_config():
    """创建示例数据集配置"""
    config = {
        'path': './dataset',
        'train': 'train/images',
        'val': 'val/images', 
        'test': 'test/images',
        'nc': 2,
        'names': ['class1', 'class2']
    }
    
    config_path = Path("configs/sample_dataset.yaml")
    config_path.parent.mkdir(exist_ok=True)
    
    import yaml
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    return str(config_path)


def cleanup_temp_files():
    """清理临时文件"""
    temp_dirs = [
        Path("temp"),
        Path("tmp"),
        Path(".cache"),
    ]
    
    for temp_dir in temp_dirs:
        if temp_dir.exists() and temp_dir.is_dir():
            try:
                import shutil
                shutil.rmtree(temp_dir)
                logging.info(f"已清理临时目录: {temp_dir}")
            except Exception as e:
                logging.warning(f"清理临时目录失败 {temp_dir}: {e}")


def get_model_info(model_path: str) -> Dict[str, Any]:
    """获取模型信息"""
    model_path = Path(model_path)
    info = {
        'exists': model_path.exists(),
        'size': 0,
        'size_formatted': '0B',
        'type': 'unknown'
    }
    
    if model_path.exists():
        info['size'] = model_path.stat().st_size
        info['size_formatted'] = format_size(info['size'])
        
        if model_path.suffix == '.pt':
            info['type'] = 'PyTorch'
        elif model_path.suffix == '.yaml':
            info['type'] = 'YAML Config'
        elif model_path.suffix == '.onnx':
            info['type'] = 'ONNX'
    
    return info
