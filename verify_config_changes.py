#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证默认配置修改
简单验证训练参数的默认值是否正确设置
"""

import sys
import platform
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config_manager import ConfigManager

def verify_config_changes():
    """验证配置修改"""
    print("验证YOLO训练器默认配置修改")
    print("=" * 50)
    
    # 检查配置管理器的默认值
    config_manager = ConfigManager()
    default_config = config_manager.get_default_config()
    
    print("检查默认配置值:")
    
    # 检查批次大小
    batch_value = default_config.get('batch')
    print(f"  批次大小 (batch): {batch_value}")
    if batch_value == 4:
        print("    ✓ 正确 - 已修改为4")
    else:
        print(f"    ❌ 错误 - 应为4，实际为{batch_value}")
    
    # 检查设备
    device_value = default_config.get('device')
    print(f"  设备 (device): {device_value}")
    if device_value == "0":
        print("    ✓ 正确 - 已修改为'0'")
    else:
        print(f"    ❌ 错误 - 应为'0'，实际为'{device_value}'")
    
    # 检查工作线程
    workers_value = default_config.get('workers')
    print(f"  工作线程 (workers): {workers_value}")
    if workers_value == 0:
        print("    ✓ 正确 - 已修改为0")
    else:
        print(f"    ❌ 错误 - 应为0，实际为{workers_value}")
    
    print("\n系统信息:")
    current_system = platform.system()
    print(f"  当前系统: {current_system}")
    
    if current_system == "Windows":
        print("  💡 Windows系统特性:")
        print("    • 工作线程设置为0可避免多进程问题")
        print("    • GUI会显示相应的警告提示")
    else:
        print("  💡 Linux/Mac系统特性:")
        print("    • 可以设置工作线程为CPU核心数以提高性能")
        print("    • GUI会显示相应的性能提示")
    
    print("\n修改总结:")
    print("  📊 批次大小: 16 → 4 (减少内存使用)")
    print("  🖥️ 设备: 'auto' → '0' (直接使用GPU 0)")
    print("  🔧 工作线程: 8 → 0 (Windows兼容性)")
    
    print("\n新增功能:")
    print("  ⚠️ Windows系统工作线程警告")
    print("  🎨 动态警告文本和颜色")
    print("  🔄 系统自适应提示")
    
    return True

def show_gui_features():
    """显示GUI新功能"""
    print("\n" + "=" * 50)
    print("GUI界面新功能")
    print("=" * 50)
    
    print("工作线程智能提示:")
    print("  • Windows系统:")
    print("    - 设置为0: 显示绿色 '✓ Windows系统推荐设置'")
    print("    - 设置>0: 显示红色 '⚠️ Windows系统建议设置为0，避免多进程问题'")
    
    print("  • Linux/Mac系统:")
    print("    - 设置为0: 显示蓝色 '💡 Linux/Mac系统可设置为CPU核心数以提高性能'")
    print("    - 设置>0: 显示绿色 '✓ 使用 N 个工作线程'")
    
    print("\n颜色编码:")
    print("  🟢 绿色 (#28a745): 推荐设置")
    print("  🔴 红色 (#dc3545): 警告设置")
    print("  🔵 蓝色 (#17a2b8): 信息提示")

def main():
    """主函数"""
    try:
        verify_config_changes()
        show_gui_features()
        
        print("\n" + "=" * 50)
        print("🎉 默认配置修改验证完成！")
        print("\n要体验新功能，请运行:")
        print("  python run_yolo_trainer.py")
        
    except Exception as e:
        print(f"\n❌ 验证失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
