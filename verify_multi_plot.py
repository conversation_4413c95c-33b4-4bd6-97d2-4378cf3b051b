#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证多标签页训练曲线图功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def verify_multi_plot():
    """验证多标签页曲线图功能"""
    print("验证多标签页训练曲线图功能...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from multi_plot_widget import MultiPlotWidget
        
        app = QApplication([])
        
        # 创建多标签页组件
        multi_plot = MultiPlotWidget()
        print("✓ MultiPlotWidget创建成功")
        
        # 检查标签页数量
        tab_count = multi_plot.tab_widget.count()
        print(f"✓ 标签页数量: {tab_count}")
        
        # 检查每个标签页
        for i in range(tab_count):
            tab_name = multi_plot.tab_widget.tabText(i)
            print(f"✓ 标签页 {i+1}: {tab_name}")
        
        # 测试数据添加
        test_metrics = {
            'box_mAP50': 0.5,
            'box_mAP50-95': 0.3,
            'box_precision': 0.7,
            'box_recall': 0.6,
            'train_loss': 1.2,
            'val_loss': 1.1,
            'seg_mAP50': 0.45,
            'top1_acc': 0.8
        }
        
        multi_plot.add_epoch_data(1, test_metrics)
        print("✓ 测试数据添加成功")
        
        # 检查数据是否正确存储
        data_stored = False
        for plot_widget in multi_plot.plot_widgets.values():
            if plot_widget.epochs and plot_widget.metrics_data:
                data_stored = True
                break
        
        if data_stored:
            print("✓ 数据存储正确")
        else:
            print("❌ 数据存储失败")
            return False
        
        # 测试清空功能
        multi_plot.clear_all_data()
        all_cleared = True
        for plot_widget in multi_plot.plot_widgets.values():
            if plot_widget.epochs or plot_widget.metrics_data:
                all_cleared = False
                break
        
        if all_cleared:
            print("✓ 数据清空功能正常")
        else:
            print("❌ 数据清空功能异常")
            return False
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def verify_integration():
    """验证与主GUI的集成"""
    print("\n验证与主GUI的集成...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from yolo_trainer_gui import YOLOTrainerGUI
        
        app = QApplication([])
        gui = YOLOTrainerGUI()
        
        # 检查是否有多标签页曲线图组件
        if hasattr(gui, 'training_plot'):
            print("✓ 训练曲线图组件已集成")
            
            # 检查组件类型
            from multi_plot_widget import MultiPlotWidget
            if isinstance(gui.training_plot, MultiPlotWidget):
                print("✓ 组件类型正确 (MultiPlotWidget)")
                
                # 检查标签页
                tab_count = gui.training_plot.tab_widget.count()
                print(f"✓ 集成的标签页数量: {tab_count}")
                
            else:
                print("❌ 组件类型错误")
                return False
        else:
            print("❌ 训练曲线图组件未集成")
            return False
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ 集成验证失败: {e}")
        return False

def show_feature_summary():
    """显示功能总结"""
    print("\n" + "=" * 60)
    print("多标签页训练曲线图功能总结")
    print("=" * 60)
    
    print("✅ 新增功能:")
    print("  📊 多标签页设计 - 按指标类型分组显示")
    print("  🎯 mAP指标标签页 - 显示各种mAP值")
    print("  📈 精度召回标签页 - 显示precision和recall")
    print("  📉 损失函数标签页 - 显示各种loss值")
    print("  🎓 分类准确率标签页 - 显示top1和top5准确率")
    print("  🎨 每个标签页独立的指标选择控制")
    print("  💾 支持保存所有标签页的图表")
    print("  🔄 一键清空所有数据")
    
    print("\n📊 标签页详情:")
    print("  1. mAP指标:")
    print("     - box_mAP50, box_mAP50-95")
    print("     - seg_mAP50, seg_mAP50-95")
    print("     - pose_mAP50, pose_mAP50-95")
    
    print("  2. 精度召回:")
    print("     - box_precision, box_recall")
    print("     - seg_precision, seg_recall")
    
    print("  3. 损失函数:")
    print("     - train_loss, val_loss")
    print("     - box_loss, seg_loss, cls_loss, dfl_loss")
    
    print("  4. 分类准确率:")
    print("     - top1_acc, top5_acc")
    
    print("\n🎨 界面改进:")
    print("  • 清晰的标签页分组，避免单页面过于拥挤")
    print("  • 每个标签页独立的控制面板")
    print("  • 统一的颜色方案和样式")
    print("  • 自动缩放和网格显示选项")
    
    print("\n🚀 使用方法:")
    print("  1. 启动训练后，切换到'监控'→'训练曲线'")
    print("  2. 在不同标签页中查看对应类型的指标")
    print("  3. 使用复选框选择要显示的具体指标")
    print("  4. 可以同时观察多个标签页的变化趋势")
    print("  5. 支持保存单个或所有标签页的图表")

def main():
    """主函数"""
    print("YOLO训练器 - 多标签页训练曲线图功能验证")
    print("=" * 60)
    
    success = True
    
    # 验证组件功能
    if not verify_multi_plot():
        success = False
    
    # 验证集成
    if not verify_integration():
        success = False
    
    # 显示功能总结
    show_feature_summary()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 多标签页训练曲线图功能验证完成！所有功能正常。")
        print("\n💡 建议:")
        print("  • 现在可以启动完整的训练来测试实际效果")
        print("  • 训练过程中可以实时切换不同标签页观察指标")
        print("  • 每个标签页都支持独立的指标选择和控制")
    else:
        print("❌ 验证过程中发现问题，请检查上述错误信息。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
