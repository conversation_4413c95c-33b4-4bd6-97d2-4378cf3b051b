#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证训练曲线图功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def verify_dependencies():
    """验证依赖项"""
    print("验证训练曲线图功能依赖...")
    
    # 检查matplotlib
    try:
        import matplotlib
        print(f"✓ matplotlib版本: {matplotlib.__version__}")
        
        # 检查后端
        import matplotlib.pyplot as plt
        backend = plt.get_backend()
        print(f"✓ matplotlib后端: {backend}")
        
    except ImportError:
        print("❌ matplotlib未安装")
        print("请安装: pip install matplotlib")
        return False
    
    # 检查PySide6
    try:
        from PySide6.QtWidgets import QApplication
        print("✓ PySide6可用")
    except ImportError:
        print("❌ PySide6未安装")
        return False
    
    return True

def verify_components():
    """验证组件"""
    print("\n验证组件导入...")

    try:
        from PySide6.QtWidgets import QApplication
        app = QApplication([])

        from training_plot_widget import TrainingPlotWidget
        print("✓ TrainingPlotWidget导入成功")

        # 检查组件属性
        widget = TrainingPlotWidget()
        print(f"✓ 支持的指标数量: {len(widget.available_metrics)}")
        print(f"✓ 指标列表: {list(widget.available_metrics.keys())[:5]}...")

        app.quit()

    except Exception as e:
        print(f"❌ TrainingPlotWidget导入失败: {e}")
        return False
    
    try:
        from yolo_trainer_gui import YOLOTrainerGUI
        print("✓ YOLOTrainerGUI导入成功")
    except Exception as e:
        print(f"❌ YOLOTrainerGUI导入失败: {e}")
        return False
    
    return True

def verify_integration():
    """验证集成"""
    print("\n验证界面集成...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from yolo_trainer_gui import YOLOTrainerGUI
        
        app = QApplication([])
        gui = YOLOTrainerGUI()
        
        # 检查是否有训练曲线图组件
        if hasattr(gui, 'training_plot'):
            print("✓ 训练曲线图组件已集成")
            
            # 检查组件类型
            from training_plot_widget import TrainingPlotWidget
            if isinstance(gui.training_plot, TrainingPlotWidget):
                print("✓ 组件类型正确")
            else:
                print("❌ 组件类型错误")
                return False
        else:
            print("❌ 训练曲线图组件未集成")
            return False
        
        app.quit()
        
    except Exception as e:
        print(f"❌ 界面集成验证失败: {e}")
        return False
    
    return True

def verify_data_flow():
    """验证数据流"""
    print("\n验证数据流...")
    
    try:
        from training_plot_widget import TrainingPlotWidget
        
        widget = TrainingPlotWidget()
        
        # 测试数据添加
        test_metrics = {
            'box_mAP50': 0.5,
            'box_mAP50-95': 0.3,
            'train_loss': 1.2,
            'val_loss': 1.1
        }
        
        widget.add_epoch_data(1, test_metrics)
        
        # 检查数据是否正确存储
        if 1 in widget.epochs:
            print("✓ epoch数据存储正确")
        else:
            print("❌ epoch数据存储失败")
            return False
        
        if 'box_mAP50' in widget.metrics_data:
            print("✓ 指标数据存储正确")
        else:
            print("❌ 指标数据存储失败")
            return False
        
        # 测试数据清空
        widget.clear_data()
        if len(widget.epochs) == 0 and len(widget.metrics_data) == 0:
            print("✓ 数据清空功能正常")
        else:
            print("❌ 数据清空功能异常")
            return False
        
    except Exception as e:
        print(f"❌ 数据流验证失败: {e}")
        return False
    
    return True

def show_feature_summary():
    """显示功能总结"""
    print("\n" + "=" * 50)
    print("训练曲线图功能总结")
    print("=" * 50)
    
    print("✅ 已实现的功能:")
    print("  📊 实时训练曲线图显示")
    print("  🎯 支持12种主要训练指标")
    print("  🎨 彩色曲线区分不同指标")
    print("  ⚙️ 交互式指标选择控制")
    print("  💾 图表导出功能 (PNG/PDF/SVG)")
    print("  🔄 数据清空和重置功能")
    print("  📱 响应式界面设计")
    
    print("\n📈 支持的指标类型:")
    print("  🎯 检测: box_mAP50, box_mAP50-95, box_precision, box_recall")
    print("  🎨 分割: seg_mAP50, seg_mAP50-95, seg_precision, seg_recall")
    print("  🤸 姿态: pose_mAP50, pose_mAP50-95")
    print("  🖼️ 分类: top1_acc, top5_acc")
    print("  📉 损失: train_loss, val_loss")
    
    print("\n🔧 技术特点:")
    print("  • 基于matplotlib专业绘图库")
    print("  • Qt5Agg后端，完美集成PySide6")
    print("  • 实时数据更新，无延迟")
    print("  • 内存优化，支持长时间训练")
    print("  • 高质量图表输出")
    
    print("\n🎮 使用方法:")
    print("  1. 启动YOLO训练器: python run_yolo_trainer.py")
    print("  2. 配置训练参数并开始训练")
    print("  3. 切换到'监控'标签页的'训练曲线'子标签")
    print("  4. 观察实时更新的训练曲线")
    print("  5. 使用复选框选择要显示的指标")
    print("  6. 可保存图表或清空数据")

def main():
    """主函数"""
    print("YOLO训练器 - 训练曲线图功能验证")
    print("=" * 50)
    
    success = True
    
    # 验证依赖项
    if not verify_dependencies():
        success = False
    
    # 验证组件
    if not verify_components():
        success = False
    
    # 验证集成
    if not verify_integration():
        success = False
    
    # 验证数据流
    if not verify_data_flow():
        success = False
    
    # 显示功能总结
    show_feature_summary()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 训练曲线图功能验证完成！所有功能正常。")
    else:
        print("❌ 验证过程中发现问题，请检查上述错误信息。")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
