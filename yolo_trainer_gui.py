#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO可视化训练界面
基于PySide6和ultralytics库实现的YOLO模型训练GUI
"""

import sys
import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QGroupBox, QFormLayout, QLineEdit, QSpinBox,
    QDoubleSpinBox, QComboBox, QCheckBox, QPushButton, QTextEdit,
    QProgressBar, QLabel, QFileDialog, QMessageBox, QSplitter,
    QTableWidget, QTableWidgetItem, QHeaderView, QScrollArea
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSettings
from PySide6.QtGui import <PERSON>Font, QPixmap, QIcon

from model_downloader import ModelDownloader
from training_worker import TrainingWorker
from config_manager import ConfigManager
from utils import (setup_logging, create_models_dir, get_model_info,
                   auto_detect_dataset, analyze_model_task)

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


class YOLOTrainerGUI(QMainWindow):
    """YOLO训练器主界面"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("YOLO可视化训练界面 v1.0")
        self.setGeometry(100, 100, 1400, 900)
        
        # 初始化组件
        self.config_manager = ConfigManager()
        self.model_downloader = ModelDownloader()
        self.training_worker = None
        self.settings = QSettings("YOLOTrainer", "Settings")
        
        # 创建models目录
        create_models_dir()
        
        # 初始化UI
        self.init_ui()
        self.load_settings()
        
        # 连接信号
        self.connect_signals()
        
    def init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧配置面板
        config_widget = self.create_config_panel()
        splitter.addWidget(config_widget)
        
        # 右侧监控面板
        monitor_widget = self.create_monitor_panel()
        splitter.addWidget(monitor_widget)
        
        # 设置分割器比例
        splitter.setSizes([600, 800])
        
    def create_config_panel(self) -> QWidget:
        """创建配置面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 模型配置标签页
        model_tab = self.create_model_tab()
        tab_widget.addTab(model_tab, "模型配置")
        
        # 数据集配置标签页
        dataset_tab = self.create_dataset_tab()
        tab_widget.addTab(dataset_tab, "数据集配置")
        
        # 训练参数标签页
        training_tab = self.create_training_tab()
        tab_widget.addTab(training_tab, "训练参数")
        
        # 增强参数标签页
        augment_tab = self.create_augment_tab()
        tab_widget.addTab(augment_tab, "数据增强")
        
        # 控制按钮
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("开始训练")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")
        
        self.stop_btn = QPushButton("停止训练")
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("QPushButton { background-color: #f44336; color: white; font-weight: bold; }")
        
        self.save_config_btn = QPushButton("保存配置")
        self.load_config_btn = QPushButton("加载配置")
        
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.save_config_btn)
        control_layout.addWidget(self.load_config_btn)
        
        layout.addLayout(control_layout)
        
        return widget
        
    def create_model_tab(self) -> QWidget:
        """创建模型配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模型选择组
        model_group = QGroupBox("模型选择")
        model_layout = QFormLayout(model_group)
        
        # 预训练模型选择
        self.model_combo = QComboBox()
        self.model_combo.addItems([
            "yolo11n.pt", "yolo11s.pt", "yolo11m.pt", 
            "yolo11l.pt", "yolo11x.pt", "自定义模型..."
        ])
        model_layout.addRow("预训练模型:", self.model_combo)
        
        # 自定义模型路径
        model_path_layout = QHBoxLayout()
        self.model_path_edit = QLineEdit()
        self.model_path_edit.setEnabled(False)
        self.browse_model_btn = QPushButton("浏览...")
        self.browse_model_btn.setEnabled(False)
        model_path_layout.addWidget(self.model_path_edit)
        model_path_layout.addWidget(self.browse_model_btn)
        model_layout.addRow("自定义路径:", model_path_layout)
        
        # 下载模型按钮
        download_layout = QHBoxLayout()
        self.download_btn = QPushButton("下载预训练模型")
        self.analyze_model_btn = QPushButton("分析模型")
        self.download_progress = QProgressBar()
        self.download_progress.setVisible(False)
        download_layout.addWidget(self.download_btn)
        download_layout.addWidget(self.analyze_model_btn)
        download_layout.addWidget(self.download_progress)
        model_layout.addRow("", download_layout)

        # 模型信息显示
        self.model_info_label = QLabel("选择模型后显示详细信息")
        self.model_info_label.setWordWrap(True)
        self.model_info_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border-radius: 3px; }")
        model_layout.addRow("模型信息:", self.model_info_label)
        
        layout.addWidget(model_group)
        
        # 任务类型组
        task_group = QGroupBox("任务类型")
        task_layout = QFormLayout(task_group)
        
        self.task_combo = QComboBox()
        self.task_combo.addItems(["detect", "segment", "classify", "pose", "obb"])
        task_layout.addRow("任务类型:", self.task_combo)
        
        layout.addWidget(task_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return widget

    def create_dataset_tab(self) -> QWidget:
        """创建数据集配置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 数据集路径组
        dataset_group = QGroupBox("数据集配置")
        dataset_layout = QFormLayout(dataset_group)

        # 数据集路径
        dataset_path_layout = QHBoxLayout()
        self.dataset_path_edit = QLineEdit()
        self.browse_dataset_btn = QPushButton("浏览...")
        self.auto_detect_btn = QPushButton("自动识别")
        self.auto_detect_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; }")
        dataset_path_layout.addWidget(self.dataset_path_edit)
        dataset_path_layout.addWidget(self.browse_dataset_btn)
        dataset_path_layout.addWidget(self.auto_detect_btn)
        dataset_layout.addRow("数据集路径:", dataset_path_layout)

        # 训练集路径
        train_path_layout = QHBoxLayout()
        self.train_path_edit = QLineEdit("train")
        self.browse_train_btn = QPushButton("浏览...")
        train_path_layout.addWidget(self.train_path_edit)
        train_path_layout.addWidget(self.browse_train_btn)
        dataset_layout.addRow("训练集:", train_path_layout)

        # 验证集路径
        val_path_layout = QHBoxLayout()
        self.val_path_edit = QLineEdit("val")
        self.browse_val_btn = QPushButton("浏览...")
        val_path_layout.addWidget(self.val_path_edit)
        val_path_layout.addWidget(self.browse_val_btn)
        dataset_layout.addRow("验证集:", val_path_layout)

        # 测试集路径
        test_path_layout = QHBoxLayout()
        self.test_path_edit = QLineEdit("test")
        self.browse_test_btn = QPushButton("浏览...")
        test_path_layout.addWidget(self.test_path_edit)
        test_path_layout.addWidget(self.browse_test_btn)
        dataset_layout.addRow("测试集:", test_path_layout)

        # 类别数量
        self.num_classes_spin = QSpinBox()
        self.num_classes_spin.setRange(1, 1000)
        self.num_classes_spin.setValue(80)
        dataset_layout.addRow("类别数量:", self.num_classes_spin)

        # 类别名称
        self.class_names_edit = QTextEdit()
        self.class_names_edit.setMaximumHeight(100)
        self.class_names_edit.setPlaceholderText("输入类别名称，每行一个...")
        dataset_layout.addRow("类别名称:", self.class_names_edit)

        # 数据集信息显示
        self.dataset_info_label = QLabel("选择数据集路径后点击'自动识别'")
        self.dataset_info_label.setWordWrap(True)
        self.dataset_info_label.setMaximumHeight(80)
        self.dataset_info_label.setStyleSheet("QLabel { background-color: #f0f0f0; padding: 5px; border-radius: 3px; }")
        dataset_layout.addRow("数据集信息:", self.dataset_info_label)

        layout.addWidget(dataset_group)
        layout.addStretch()

        return widget

    def create_training_tab(self) -> QWidget:
        """创建训练参数标签页"""
        widget = QWidget()
        scroll = QScrollArea()
        scroll.setWidget(widget)
        scroll.setWidgetResizable(True)

        layout = QVBoxLayout(widget)

        # 基础训练参数
        basic_group = QGroupBox("基础参数")
        basic_layout = QFormLayout(basic_group)

        # 训练轮数
        self.epochs_spin = QSpinBox()
        self.epochs_spin.setRange(1, 10000)
        self.epochs_spin.setValue(100)
        basic_layout.addRow("训练轮数 (epochs):", self.epochs_spin)

        # 批次大小
        self.batch_spin = QSpinBox()
        self.batch_spin.setRange(-1, 512)
        self.batch_spin.setValue(16)
        basic_layout.addRow("批次大小 (batch):", self.batch_spin)

        # 图像尺寸
        self.imgsz_spin = QSpinBox()
        self.imgsz_spin.setRange(32, 2048)
        self.imgsz_spin.setValue(640)
        self.imgsz_spin.setSingleStep(32)
        basic_layout.addRow("图像尺寸 (imgsz):", self.imgsz_spin)

        # 设备选择
        self.device_combo = QComboBox()
        self.device_combo.addItems(["auto", "cpu", "0", "1", "0,1", "mps"])
        basic_layout.addRow("设备 (device):", self.device_combo)

        # 工作线程数
        self.workers_spin = QSpinBox()
        self.workers_spin.setRange(0, 32)
        self.workers_spin.setValue(8)
        basic_layout.addRow("工作线程 (workers):", self.workers_spin)

        layout.addWidget(basic_group)

        # 优化器参数
        optimizer_group = QGroupBox("优化器参数")
        optimizer_layout = QFormLayout(optimizer_group)

        # 优化器选择
        self.optimizer_combo = QComboBox()
        self.optimizer_combo.addItems(["auto", "SGD", "Adam", "AdamW", "NAdam", "RAdam", "RMSProp"])
        optimizer_layout.addRow("优化器:", self.optimizer_combo)

        # 初始学习率
        self.lr0_spin = QDoubleSpinBox()
        self.lr0_spin.setRange(0.0001, 1.0)
        self.lr0_spin.setValue(0.01)
        self.lr0_spin.setDecimals(4)
        self.lr0_spin.setSingleStep(0.001)
        optimizer_layout.addRow("初始学习率 (lr0):", self.lr0_spin)

        # 最终学习率比例
        self.lrf_spin = QDoubleSpinBox()
        self.lrf_spin.setRange(0.0001, 1.0)
        self.lrf_spin.setValue(0.01)
        self.lrf_spin.setDecimals(4)
        self.lrf_spin.setSingleStep(0.001)
        optimizer_layout.addRow("最终学习率比例 (lrf):", self.lrf_spin)

        # 动量
        self.momentum_spin = QDoubleSpinBox()
        self.momentum_spin.setRange(0.0, 1.0)
        self.momentum_spin.setValue(0.937)
        self.momentum_spin.setDecimals(3)
        self.momentum_spin.setSingleStep(0.001)
        optimizer_layout.addRow("动量 (momentum):", self.momentum_spin)

        # 权重衰减
        self.weight_decay_spin = QDoubleSpinBox()
        self.weight_decay_spin.setRange(0.0, 1.0)
        self.weight_decay_spin.setValue(0.0005)
        self.weight_decay_spin.setDecimals(4)
        self.weight_decay_spin.setSingleStep(0.0001)
        optimizer_layout.addRow("权重衰减 (weight_decay):", self.weight_decay_spin)

        layout.addWidget(optimizer_group)

        # 高级参数
        advanced_group = QGroupBox("高级参数")
        advanced_layout = QFormLayout(advanced_group)

        # 预热轮数
        self.warmup_epochs_spin = QDoubleSpinBox()
        self.warmup_epochs_spin.setRange(0.0, 10.0)
        self.warmup_epochs_spin.setValue(3.0)
        self.warmup_epochs_spin.setDecimals(1)
        advanced_layout.addRow("预热轮数 (warmup_epochs):", self.warmup_epochs_spin)

        # 耐心值
        self.patience_spin = QSpinBox()
        self.patience_spin.setRange(1, 1000)
        self.patience_spin.setValue(100)
        advanced_layout.addRow("早停耐心值 (patience):", self.patience_spin)

        # 保存周期
        self.save_period_spin = QSpinBox()
        self.save_period_spin.setRange(-1, 1000)
        self.save_period_spin.setValue(-1)
        advanced_layout.addRow("保存周期 (save_period):", self.save_period_spin)

        # 混合精度训练
        self.amp_check = QCheckBox()
        self.amp_check.setChecked(True)
        advanced_layout.addRow("混合精度训练 (amp):", self.amp_check)

        # 余弦学习率
        self.cos_lr_check = QCheckBox()
        self.cos_lr_check.setChecked(False)
        advanced_layout.addRow("余弦学习率 (cos_lr):", self.cos_lr_check)

        layout.addWidget(advanced_group)
        layout.addStretch()

        return scroll

    def create_augment_tab(self) -> QWidget:
        """创建数据增强标签页"""
        widget = QWidget()
        scroll = QScrollArea()
        scroll.setWidget(widget)
        scroll.setWidgetResizable(True)

        layout = QVBoxLayout(widget)

        # 颜色增强
        color_group = QGroupBox("颜色增强")
        color_layout = QFormLayout(color_group)

        # HSV色调
        self.hsv_h_spin = QDoubleSpinBox()
        self.hsv_h_spin.setRange(0.0, 1.0)
        self.hsv_h_spin.setValue(0.015)
        self.hsv_h_spin.setDecimals(3)
        self.hsv_h_spin.setSingleStep(0.001)
        color_layout.addRow("HSV色调 (hsv_h):", self.hsv_h_spin)

        # HSV饱和度
        self.hsv_s_spin = QDoubleSpinBox()
        self.hsv_s_spin.setRange(0.0, 1.0)
        self.hsv_s_spin.setValue(0.7)
        self.hsv_s_spin.setDecimals(3)
        self.hsv_s_spin.setSingleStep(0.01)
        color_layout.addRow("HSV饱和度 (hsv_s):", self.hsv_s_spin)

        # HSV亮度
        self.hsv_v_spin = QDoubleSpinBox()
        self.hsv_v_spin.setRange(0.0, 1.0)
        self.hsv_v_spin.setValue(0.4)
        self.hsv_v_spin.setDecimals(3)
        self.hsv_v_spin.setSingleStep(0.01)
        color_layout.addRow("HSV亮度 (hsv_v):", self.hsv_v_spin)

        layout.addWidget(color_group)

        # 几何变换
        geometric_group = QGroupBox("几何变换")
        geometric_layout = QFormLayout(geometric_group)

        # 旋转角度
        self.degrees_spin = QDoubleSpinBox()
        self.degrees_spin.setRange(0.0, 180.0)
        self.degrees_spin.setValue(0.0)
        self.degrees_spin.setDecimals(1)
        geometric_layout.addRow("旋转角度 (degrees):", self.degrees_spin)

        # 平移
        self.translate_spin = QDoubleSpinBox()
        self.translate_spin.setRange(0.0, 1.0)
        self.translate_spin.setValue(0.1)
        self.translate_spin.setDecimals(3)
        self.translate_spin.setSingleStep(0.01)
        geometric_layout.addRow("平移 (translate):", self.translate_spin)

        # 缩放
        self.scale_spin = QDoubleSpinBox()
        self.scale_spin.setRange(0.0, 2.0)
        self.scale_spin.setValue(0.5)
        self.scale_spin.setDecimals(3)
        self.scale_spin.setSingleStep(0.01)
        geometric_layout.addRow("缩放 (scale):", self.scale_spin)

        # 剪切
        self.shear_spin = QDoubleSpinBox()
        self.shear_spin.setRange(-180.0, 180.0)
        self.shear_spin.setValue(0.0)
        self.shear_spin.setDecimals(1)
        geometric_layout.addRow("剪切 (shear):", self.shear_spin)

        # 透视变换
        self.perspective_spin = QDoubleSpinBox()
        self.perspective_spin.setRange(0.0, 0.001)
        self.perspective_spin.setValue(0.0)
        self.perspective_spin.setDecimals(6)
        self.perspective_spin.setSingleStep(0.00001)
        geometric_layout.addRow("透视变换 (perspective):", self.perspective_spin)

        layout.addWidget(geometric_group)

        # 翻转和混合
        flip_group = QGroupBox("翻转和混合")
        flip_layout = QFormLayout(flip_group)

        # 水平翻转
        self.fliplr_spin = QDoubleSpinBox()
        self.fliplr_spin.setRange(0.0, 1.0)
        self.fliplr_spin.setValue(0.5)
        self.fliplr_spin.setDecimals(3)
        self.fliplr_spin.setSingleStep(0.01)
        flip_layout.addRow("水平翻转 (fliplr):", self.fliplr_spin)

        # 垂直翻转
        self.flipud_spin = QDoubleSpinBox()
        self.flipud_spin.setRange(0.0, 1.0)
        self.flipud_spin.setValue(0.0)
        self.flipud_spin.setDecimals(3)
        self.flipud_spin.setSingleStep(0.01)
        flip_layout.addRow("垂直翻转 (flipud):", self.flipud_spin)

        # Mosaic增强
        self.mosaic_spin = QDoubleSpinBox()
        self.mosaic_spin.setRange(0.0, 1.0)
        self.mosaic_spin.setValue(1.0)
        self.mosaic_spin.setDecimals(3)
        self.mosaic_spin.setSingleStep(0.01)
        flip_layout.addRow("Mosaic增强 (mosaic):", self.mosaic_spin)

        # Mixup增强
        self.mixup_spin = QDoubleSpinBox()
        self.mixup_spin.setRange(0.0, 1.0)
        self.mixup_spin.setValue(0.0)
        self.mixup_spin.setDecimals(3)
        self.mixup_spin.setSingleStep(0.01)
        flip_layout.addRow("Mixup增强 (mixup):", self.mixup_spin)

        layout.addWidget(flip_group)
        layout.addStretch()

        return scroll

    def create_monitor_panel(self) -> QWidget:
        """创建监控面板"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 状态信息
        status_group = QGroupBox("训练状态")
        status_layout = QVBoxLayout(status_group)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; color: #2196F3; }")
        status_layout.addWidget(self.status_label)

        # 当前轮次信息
        self.epoch_label = QLabel("轮次: 0/0")
        status_layout.addWidget(self.epoch_label)

        layout.addWidget(status_group)

        # 训练日志
        log_group = QGroupBox("训练日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)

        # 日志控制按钮
        log_control_layout = QHBoxLayout()
        self.clear_log_btn = QPushButton("清空日志")
        self.save_log_btn = QPushButton("保存日志")
        log_control_layout.addWidget(self.clear_log_btn)
        log_control_layout.addWidget(self.save_log_btn)
        log_control_layout.addStretch()
        log_layout.addLayout(log_control_layout)

        layout.addWidget(log_group)

        return widget

    def connect_signals(self):
        """连接信号和槽"""
        # 模型选择相关
        self.model_combo.currentTextChanged.connect(self.on_model_changed)
        self.browse_model_btn.clicked.connect(self.browse_model_file)
        self.download_btn.clicked.connect(self.download_model)
        self.analyze_model_btn.clicked.connect(self.analyze_current_model)

        # 数据集相关
        self.browse_dataset_btn.clicked.connect(self.browse_dataset_path)
        self.auto_detect_btn.clicked.connect(self.auto_detect_dataset)
        self.browse_train_btn.clicked.connect(self.browse_train_path)
        self.browse_val_btn.clicked.connect(self.browse_val_path)
        self.browse_test_btn.clicked.connect(self.browse_test_path)

        # 控制按钮
        self.start_btn.clicked.connect(self.start_training)
        self.stop_btn.clicked.connect(self.stop_training)
        self.save_config_btn.clicked.connect(self.save_config)
        self.load_config_btn.clicked.connect(self.load_config)

        # 日志控制
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.save_log_btn.clicked.connect(self.save_log)

        # 模型下载器信号
        self.model_downloader.progress_updated.connect(self.update_download_progress)
        self.model_downloader.download_finished.connect(self.on_download_finished)
        self.model_downloader.download_error.connect(self.on_download_error)

    def on_model_changed(self, model_name: str):
        """模型选择改变时的处理"""
        is_custom = model_name == "自定义模型..."
        self.model_path_edit.setEnabled(is_custom)
        self.browse_model_btn.setEnabled(is_custom)

    def browse_model_file(self):
        """浏览模型文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择模型文件", "", "模型文件 (*.pt *.yaml);;所有文件 (*)"
        )
        if file_path:
            self.model_path_edit.setText(file_path)

    def browse_dataset_path(self):
        """浏览数据集路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择数据集目录")
        if dir_path:
            self.dataset_path_edit.setText(dir_path)

    def browse_train_path(self):
        """浏览训练集路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择训练集目录")
        if dir_path:
            self.train_path_edit.setText(dir_path)

    def browse_val_path(self):
        """浏览验证集路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择验证集目录")
        if dir_path:
            self.val_path_edit.setText(dir_path)

    def browse_test_path(self):
        """浏览测试集路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择测试集目录")
        if dir_path:
            self.test_path_edit.setText(dir_path)

    def download_model(self):
        """下载预训练模型"""
        model_name = self.model_combo.currentText()
        if model_name == "自定义模型...":
            QMessageBox.warning(self, "警告", "请选择一个预训练模型进行下载")
            return

        # 检查模型是否已存在
        if self.model_downloader.is_model_downloaded(model_name):
            reply = QMessageBox.question(
                self, "模型已存在",
                f"模型 {model_name} 已存在，是否重新下载？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.No:
                return

        self.download_btn.setEnabled(False)
        self.download_progress.setVisible(True)
        self.download_progress.setValue(0)
        self.log_message(f"开始下载模型: {model_name}")

        # 开始下载
        self.model_downloader.download_model(model_name)

    def update_download_progress(self, progress: int):
        """更新下载进度"""
        self.download_progress.setValue(progress)

    def on_download_finished(self, model_path: str):
        """下载完成处理"""
        self.download_btn.setEnabled(True)
        self.download_progress.setVisible(False)
        self.log_message(f"模型下载完成: {model_path}")
        QMessageBox.information(self, "下载完成", f"模型已下载到: {model_path}")

    def on_download_error(self, error_msg: str):
        """下载错误处理"""
        self.download_btn.setEnabled(True)
        self.download_progress.setVisible(False)
        self.log_message(f"下载失败: {error_msg}")
        QMessageBox.critical(self, "下载失败", f"下载失败: {error_msg}")

    def analyze_current_model(self):
        """分析当前选择的模型"""
        try:
            # 获取模型路径
            if self.model_combo.currentText() == "自定义模型...":
                model_path = self.model_path_edit.text()
                if not model_path:
                    QMessageBox.warning(self, "警告", "请先选择自定义模型文件")
                    return
            else:
                model_name = self.model_combo.currentText()
                model_path = f"models/{model_name}"

            if not os.path.exists(model_path):
                QMessageBox.warning(self, "警告", f"模型文件不存在: {model_path}")
                return

            self.log_message(f"正在分析模型: {model_path}")

            # 分析模型信息
            model_info = get_model_info(model_path)

            # 更新界面显示
            self.update_model_info_display(model_info)

            # 自动设置任务类型
            if model_info.get('task') != 'unknown':
                task_index = self.task_combo.findText(model_info['task'])
                if task_index >= 0:
                    self.task_combo.setCurrentIndex(task_index)
                    self.log_message(f"自动设置任务类型: {model_info['task']}")

            self.log_message("模型分析完成")

        except Exception as e:
            error_msg = f"模型分析失败: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "分析失败", error_msg)

    def update_model_info_display(self, model_info: dict):
        """更新模型信息显示"""
        info_text = []

        if model_info.get('exists'):
            info_text.append(f"✓ 模型文件存在")
            info_text.append(f"📁 大小: {model_info.get('size_formatted', 'Unknown')}")
            info_text.append(f"🏗️ 类型: {model_info.get('type', 'Unknown')}")

            if model_info.get('task') != 'unknown':
                info_text.append(f"🎯 任务: {model_info.get('task', 'Unknown')}")

            if model_info.get('num_classes', 0) > 0:
                info_text.append(f"📊 类别数: {model_info.get('num_classes', 0)}")

            if model_info.get('architecture'):
                info_text.append(f"🔧 架构: {model_info.get('architecture', 'Unknown')}")
        else:
            info_text.append("❌ 模型文件不存在")

        self.model_info_label.setText("\n".join(info_text))

    def auto_detect_dataset(self):
        """自动检测数据集"""
        dataset_path = self.dataset_path_edit.text().strip()

        if not dataset_path:
            QMessageBox.warning(self, "警告", "请先选择数据集路径")
            return

        if not os.path.exists(dataset_path):
            QMessageBox.warning(self, "警告", f"数据集路径不存在: {dataset_path}")
            return

        try:
            self.log_message(f"正在自动检测数据集: {dataset_path}")

            # 自动检测数据集
            dataset_info = auto_detect_dataset(dataset_path)

            # 更新界面显示
            self.update_dataset_info_display(dataset_info)

            # 自动填充配置
            if dataset_info.get('valid'):
                # 设置类别数量
                if dataset_info.get('num_classes', 0) > 0:
                    self.num_classes_spin.setValue(dataset_info['num_classes'])

                # 设置类别名称
                if dataset_info.get('class_names'):
                    class_names_text = '\n'.join(dataset_info['class_names'])
                    self.class_names_edit.setPlainText(class_names_text)

                # 自动设置任务类型（如果检测到）
                if dataset_info.get('task') != 'unknown':
                    task_index = self.task_combo.findText(dataset_info['task'])
                    if task_index >= 0:
                        self.task_combo.setCurrentIndex(task_index)
                        self.log_message(f"自动设置任务类型: {dataset_info['task']}")

                self.log_message("数据集自动检测完成")

                # 显示检测结果
                if dataset_info.get('warnings'):
                    warning_msg = "检测到以下问题:\n" + "\n".join(dataset_info['warnings'])
                    QMessageBox.warning(self, "数据集警告", warning_msg)
                else:
                    QMessageBox.information(self, "检测完成", "数据集检测完成，配置已自动填充")
            else:
                error_msg = "数据集检测失败:\n" + "\n".join(dataset_info.get('errors', ['未知错误']))
                QMessageBox.critical(self, "检测失败", error_msg)

        except Exception as e:
            error_msg = f"数据集检测失败: {str(e)}"
            self.log_message(error_msg)
            QMessageBox.critical(self, "检测失败", error_msg)

    def update_dataset_info_display(self, dataset_info: dict):
        """更新数据集信息显示"""
        info_text = []

        if dataset_info.get('valid'):
            info_text.append("✓ 数据集结构有效")

            # 显示分割信息
            splits = dataset_info.get('splits', [])
            if splits:
                info_text.append(f"📂 包含分割: {', '.join(splits)}")

            # 显示统计信息
            total_images = dataset_info.get('total_images', 0)
            total_labels = dataset_info.get('total_labels', 0)
            if total_images > 0:
                info_text.append(f"🖼️ 总图像数: {total_images}")
                info_text.append(f"🏷️ 总标签数: {total_labels}")

            # 显示任务类型
            if dataset_info.get('task') != 'unknown':
                info_text.append(f"🎯 检测到任务: {dataset_info.get('task')}")

            # 显示类别信息
            num_classes = dataset_info.get('num_classes', 0)
            if num_classes > 0:
                info_text.append(f"📊 类别数量: {num_classes}")
                class_source = dataset_info.get('class_source', '')
                if class_source:
                    info_text.append(f"📋 类别来源: {class_source}")
        else:
            info_text.append("❌ 数据集结构无效")
            errors = dataset_info.get('errors', [])
            if errors:
                info_text.extend([f"  • {error}" for error in errors[:3]])  # 只显示前3个错误

        self.dataset_info_label.setText("\n".join(info_text))

    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置"""
        config = {}

        # 模型配置
        if self.model_combo.currentText() == "自定义模型...":
            config['model'] = self.model_path_edit.text()
        else:
            model_name = self.model_combo.currentText()
            config['model'] = f"models/{model_name}"

        config['task'] = self.task_combo.currentText()

        # 数据集配置
        dataset_config = self.config_manager.create_dataset_config(
            dataset_path=self.dataset_path_edit.text(),
            train_path=self.train_path_edit.text(),
            val_path=self.val_path_edit.text(),
            test_path=self.test_path_edit.text(),
            num_classes=self.num_classes_spin.value(),
            class_names=self.class_names_edit.toPlainText().strip().split('\n')
        )
        config['data'] = dataset_config

        # 基础训练参数
        config['epochs'] = self.epochs_spin.value()
        config['batch'] = self.batch_spin.value()
        config['imgsz'] = self.imgsz_spin.value()
        config['device'] = self.device_combo.currentText()
        config['workers'] = self.workers_spin.value()

        # 优化器参数
        config['optimizer'] = self.optimizer_combo.currentText()
        config['lr0'] = self.lr0_spin.value()
        config['lrf'] = self.lrf_spin.value()
        config['momentum'] = self.momentum_spin.value()
        config['weight_decay'] = self.weight_decay_spin.value()

        # 高级参数
        config['warmup_epochs'] = self.warmup_epochs_spin.value()
        config['patience'] = self.patience_spin.value()
        config['save_period'] = self.save_period_spin.value()
        config['amp'] = self.amp_check.isChecked()
        config['cos_lr'] = self.cos_lr_check.isChecked()

        # 数据增强参数
        config['hsv_h'] = self.hsv_h_spin.value()
        config['hsv_s'] = self.hsv_s_spin.value()
        config['hsv_v'] = self.hsv_v_spin.value()
        config['degrees'] = self.degrees_spin.value()
        config['translate'] = self.translate_spin.value()
        config['scale'] = self.scale_spin.value()
        config['shear'] = self.shear_spin.value()
        config['perspective'] = self.perspective_spin.value()
        config['fliplr'] = self.fliplr_spin.value()
        config['flipud'] = self.flipud_spin.value()
        config['mosaic'] = self.mosaic_spin.value()
        config['mixup'] = self.mixup_spin.value()

        return config

    def start_training(self):
        """开始训练"""
        try:
            # 获取训练配置
            config = self.get_training_config()

            # 验证配置
            if not self.validate_config(config):
                return

            # 创建训练工作器
            self.training_worker = TrainingWorker(config)

            # 连接信号
            self.training_worker.progress_updated.connect(self.update_training_progress)
            self.training_worker.epoch_finished.connect(self.on_epoch_finished)
            self.training_worker.training_finished.connect(self.on_training_finished)
            self.training_worker.training_error.connect(self.on_training_error)
            self.training_worker.log_message.connect(self.log_message)

            # 更新UI状态
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            self.status_label.setText("训练中...")
            self.status_label.setStyleSheet("QLabel { font-weight: bold; color: #FF9800; }")

            # 开始训练
            self.training_worker.start()
            self.log_message("开始训练...")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动训练失败: {str(e)}")
            logger.error(f"启动训练失败: {e}")

    def stop_training(self):
        """停止训练"""
        if self.training_worker and self.training_worker.isRunning():
            self.training_worker.stop()
            self.log_message("正在停止训练...")

    def validate_config(self, config: Dict[str, Any]) -> bool:
        """验证训练配置"""
        # 检查模型文件
        model_path = config.get('model', '')
        if not model_path:
            QMessageBox.warning(self, "配置错误", "请选择模型文件")
            return False

        # 检查数据集配置
        if not config.get('data'):
            QMessageBox.warning(self, "配置错误", "请配置数据集")
            return False

        return True

    def update_training_progress(self, epoch: int, total_epochs: int, progress: float):
        """更新训练进度"""
        self.progress_bar.setValue(int(progress))
        self.epoch_label.setText(f"轮次: {epoch}/{total_epochs}")

    def on_epoch_finished(self, epoch: int, metrics: Dict[str, float]):
        """轮次完成处理"""
        metrics_str = ", ".join([f"{k}: {v:.4f}" for k, v in metrics.items()])
        self.log_message(f"轮次 {epoch} 完成 - {metrics_str}")

    def on_training_finished(self, results: Dict[str, Any]):
        """训练完成处理"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练完成")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; color: #4CAF50; }")

        self.log_message("训练完成!")
        self.log_message(f"最佳模型保存在: {results.get('best_model_path', 'N/A')}")

        QMessageBox.information(self, "训练完成", "模型训练已完成!")

    def on_training_error(self, error_msg: str):
        """训练错误处理"""
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("训练失败")
        self.status_label.setStyleSheet("QLabel { font-weight: bold; color: #f44336; }")

        self.log_message(f"训练失败: {error_msg}")
        QMessageBox.critical(self, "训练失败", f"训练过程中发生错误:\n{error_msg}")

    def log_message(self, message: str):
        """添加日志消息"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def save_log(self):
        """保存日志"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存日志", "training_log.txt", "文本文件 (*.txt);;所有文件 (*)"
        )
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "保存成功", f"日志已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存日志失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置", "training_config.json", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                config = self.get_training_config()
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=2, ensure_ascii=False)
                QMessageBox.information(self, "保存成功", f"配置已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存配置失败: {str(e)}")

    def load_config(self):
        """加载配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载配置", "", "JSON文件 (*.json);;所有文件 (*)"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.apply_config(config)
                QMessageBox.information(self, "加载成功", f"配置已从 {file_path} 加载")
            except Exception as e:
                QMessageBox.critical(self, "加载失败", f"加载配置失败: {str(e)}")

    def apply_config(self, config: Dict[str, Any]):
        """应用配置到界面"""
        # 模型配置
        if 'model' in config:
            model_path = config['model']
            if model_path.startswith('models/'):
                model_name = model_path.replace('models/', '')
                index = self.model_combo.findText(model_name)
                if index >= 0:
                    self.model_combo.setCurrentIndex(index)
            else:
                self.model_combo.setCurrentText("自定义模型...")
                self.model_path_edit.setText(model_path)

        if 'task' in config:
            index = self.task_combo.findText(config['task'])
            if index >= 0:
                self.task_combo.setCurrentIndex(index)

        # 基础参数
        if 'epochs' in config:
            self.epochs_spin.setValue(config['epochs'])
        if 'batch' in config:
            self.batch_spin.setValue(config['batch'])
        if 'imgsz' in config:
            self.imgsz_spin.setValue(config['imgsz'])
        if 'device' in config:
            index = self.device_combo.findText(config['device'])
            if index >= 0:
                self.device_combo.setCurrentIndex(index)
        if 'workers' in config:
            self.workers_spin.setValue(config['workers'])

        # 优化器参数
        if 'optimizer' in config:
            index = self.optimizer_combo.findText(config['optimizer'])
            if index >= 0:
                self.optimizer_combo.setCurrentIndex(index)
        if 'lr0' in config:
            self.lr0_spin.setValue(config['lr0'])
        if 'lrf' in config:
            self.lrf_spin.setValue(config['lrf'])
        if 'momentum' in config:
            self.momentum_spin.setValue(config['momentum'])
        if 'weight_decay' in config:
            self.weight_decay_spin.setValue(config['weight_decay'])

        # 高级参数
        if 'warmup_epochs' in config:
            self.warmup_epochs_spin.setValue(config['warmup_epochs'])
        if 'patience' in config:
            self.patience_spin.setValue(config['patience'])
        if 'save_period' in config:
            self.save_period_spin.setValue(config['save_period'])
        if 'amp' in config:
            self.amp_check.setChecked(config['amp'])
        if 'cos_lr' in config:
            self.cos_lr_check.setChecked(config['cos_lr'])

        # 数据增强参数
        augment_params = [
            'hsv_h', 'hsv_s', 'hsv_v', 'degrees', 'translate', 'scale',
            'shear', 'perspective', 'fliplr', 'flipud', 'mosaic', 'mixup'
        ]
        for param in augment_params:
            if param in config:
                widget = getattr(self, f"{param}_spin", None)
                if widget:
                    widget.setValue(config[param])

    def save_settings(self):
        """保存应用设置"""
        self.settings.setValue("geometry", self.saveGeometry())
        self.settings.setValue("windowState", self.saveState())

    def load_settings(self):
        """加载应用设置"""
        geometry = self.settings.value("geometry")
        if geometry:
            self.restoreGeometry(geometry)
        window_state = self.settings.value("windowState")
        if window_state:
            self.restoreState(window_state)

    def closeEvent(self, event):
        """关闭事件处理"""
        # 停止训练
        if self.training_worker and self.training_worker.isRunning():
            reply = QMessageBox.question(
                self, "确认退出", "训练正在进行中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                self.training_worker.stop()
                self.training_worker.wait(3000)  # 等待3秒
            else:
                event.ignore()
                return

        # 保存设置
        self.save_settings()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")  # 使用现代风格
    
    window = YOLOTrainerGUI()
    window.show()
    
    sys.exit(app.exec())
