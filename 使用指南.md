# YOLO可视化训练界面 - 使用指南

## 🚀 快速开始

### 1. 启动应用程序
```bash
python run_yolo_trainer.py
```

### 2. 界面概览
应用程序启动后，您会看到：
- **左侧**: 配置面板（4个标签页）
- **右侧**: 监控面板（状态和日志）
- **底部**: 控制按钮

## 📋 详细使用步骤

### 步骤1: 模型配置
1. 在"模型配置"标签页中：
   - 选择预训练模型（推荐从yolo11n.pt开始）
   - 点击"下载预训练模型"按钮
   - 等待下载完成（会显示进度条）
   - 选择任务类型（detect/segment/classify/pose/obb）

### 步骤2: 数据集配置
1. 在"数据集配置"标签页中：
   - 点击"浏览..."选择数据集根目录
   - 设置训练集路径（默认：train）
   - 设置验证集路径（默认：val）
   - 设置测试集路径（默认：test）
   - 输入类别数量
   - 在文本框中输入类别名称（每行一个）

**数据集格式要求**：
```
dataset/
├── train/
│   ├── images/
│   │   ├── img1.jpg
│   │   └── img2.jpg
│   └── labels/
│       ├── img1.txt
│       └── img2.txt
├── val/
│   ├── images/
│   └── labels/
└── test/
    ├── images/
    └── labels/
```

### 步骤3: 训练参数配置
1. 在"训练参数"标签页中：
   - **基础参数**：
     - 训练轮数：建议50-200轮
     - 批次大小：根据GPU内存调整（8-32）
     - 图像尺寸：640（标准）或更大
     - 设备：auto（自动选择）或指定GPU
   
   - **优化器参数**：
     - 优化器：auto（推荐）
     - 初始学习率：0.01（默认）
     - 动量：0.937（默认）
   
   - **高级参数**：
     - 混合精度训练：建议开启（提高速度）
     - 早停耐心值：100（防止过拟合）

### 步骤4: 数据增强配置
1. 在"数据增强"标签页中：
   - **颜色增强**：HSV调整（默认值通常足够）
   - **几何变换**：旋转、平移、缩放等
   - **翻转和混合**：水平翻转、Mosaic增强等

### 步骤5: 开始训练
1. 检查所有配置
2. 点击"开始训练"按钮
3. 在右侧监控面板观察：
   - 训练进度条
   - 当前轮次信息
   - 实时训练日志

## 🔧 常用配置建议

### 初学者配置
```
模型: yolo11n.pt
轮数: 50
批次: 16
图像尺寸: 640
设备: auto
其他: 使用默认值
```

### 高性能配置
```
模型: yolo11l.pt 或 yolo11x.pt
轮数: 100-300
批次: 8-16（根据GPU内存）
图像尺寸: 832 或 1024
设备: 0（指定GPU）
混合精度: 开启
```

### CPU训练配置
```
模型: yolo11n.pt
轮数: 20-50
批次: 4-8
图像尺寸: 416 或 640
设备: cpu
工作线程: 4-8
```

## 📊 训练监控

### 进度信息
- **进度条**: 显示当前训练进度
- **轮次信息**: 当前轮次/总轮次
- **状态标签**: 训练状态（就绪/训练中/完成/失败）

### 日志信息
训练日志会显示：
- 每轮次的损失值
- 验证指标（mAP、精度、召回率等）
- 训练时间信息
- 错误和警告信息

### 结果文件
训练完成后，结果保存在：
```
runs/train/exp/
├── weights/
│   ├── best.pt      # 最佳模型
│   └── last.pt      # 最后一轮模型
├── results.png      # 训练曲线图
├── confusion_matrix.png
└── val_batch0_labels.jpg
```

## 💾 配置管理

### 保存配置
1. 配置好所有参数后
2. 点击"保存配置"按钮
3. 选择保存位置和文件名
4. 配置将保存为JSON格式

### 加载配置
1. 点击"加载配置"按钮
2. 选择之前保存的配置文件
3. 所有参数将自动填入界面

## ⚠️ 常见问题

### 1. 模型下载失败
**解决方案**：
- 检查网络连接
- 尝试重新下载
- 手动下载模型文件到models/目录

### 2. 训练启动失败
**可能原因**：
- 数据集路径错误
- 数据集格式不正确
- GPU内存不足

**解决方案**：
- 检查数据集路径和格式
- 减小批次大小
- 使用更小的模型

### 3. 训练速度慢
**优化建议**：
- 使用GPU训练
- 开启混合精度训练
- 增加工作线程数
- 使用更小的图像尺寸

### 4. 内存不足
**解决方案**：
- 减小批次大小
- 使用更小的模型
- 减小图像尺寸
- 关闭不必要的程序

## 🎯 训练技巧

### 数据准备
1. **数据质量**: 确保标注准确
2. **数据平衡**: 各类别样本数量尽量均衡
3. **数据增强**: 适当使用数据增强提高泛化能力

### 参数调优
1. **学习率**: 从默认值开始，根据训练曲线调整
2. **批次大小**: 在GPU内存允许范围内尽量大
3. **训练轮数**: 观察验证损失，避免过拟合

### 模型选择
1. **小数据集**: 使用yolo11n或yolo11s
2. **大数据集**: 使用yolo11m、yolo11l或yolo11x
3. **实时应用**: 优先考虑yolo11n或yolo11s

## 📞 技术支持

如果遇到问题：
1. 查看训练日志（右侧面板）
2. 检查yolo_trainer.log文件
3. 参考README.md文档
4. 运行test_gui.py检查环境

---

**祝您训练愉快！** 🎉
