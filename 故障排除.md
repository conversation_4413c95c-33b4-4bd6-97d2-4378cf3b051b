# YOLO训练器 - 故障排除指南

## 🔍 问题诊断

### 第一步：运行诊断测试
```bash
python test_gui.py
```
这会检查所有组件是否正常工作。

## 🚨 常见错误及解决方案

### 1. 启动问题

#### 错误：ModuleNotFoundError
```
ModuleNotFoundError: No module named 'PySide6'
```
**解决方案**：
```bash
pip install PySide6
# 或安装完整依赖
pip install -r requirements.txt
```

#### 错误：Python版本过低
```
错误: Python版本过低
```
**解决方案**：
- 升级到Python 3.8或更高版本
- 使用conda创建新环境：
```bash
conda create -n yolo11 python=3.8
conda activate yolo11
```

### 2. 模型下载问题

#### 错误：wget不可用
```
WARNING - wget不可用: [WinError 2] 系统找不到指定的文件
```
**说明**：这是正常警告，程序会自动使用PowerShell或requests下载。

#### 错误：下载失败
```
下载失败: 网络请求失败
```
**解决方案**：
1. 检查网络连接
2. 手动下载模型：
   - 访问：https://github.com/ultralytics/assets/releases/tag/v8.3.0
   - 下载所需模型文件
   - 放入项目的`models/`目录

#### 错误：PowerShell下载失败
**解决方案**：
1. 检查PowerShell是否可用
2. 尝试手动下载
3. 检查防火墙设置

### 3. 训练启动问题

#### 错误：数据集配置错误
```
配置错误: 请配置数据集
```
**解决方案**：
1. 确保数据集路径正确
2. 检查数据集格式：
```
dataset/
├── train/images/
├── train/labels/
├── val/images/
└── val/labels/
```

#### 错误：CUDA内存不足
```
RuntimeError: CUDA out of memory
```
**解决方案**：
1. 减小批次大小（batch）
2. 使用更小的模型（yolo11n.pt）
3. 减小图像尺寸（imgsz）
4. 关闭其他GPU程序

#### 错误：模型文件不存在
```
模型文件不存在
```
**解决方案**：
1. 重新下载模型
2. 检查models/目录
3. 使用自定义模型路径

### 4. 界面问题

#### 错误：界面无响应
**可能原因**：
- 训练线程阻塞
- 系统资源不足

**解决方案**：
1. 等待训练完成
2. 重启应用程序
3. 检查系统资源

#### 错误：进度条不更新
**解决方案**：
1. 检查训练是否真正开始
2. 查看日志输出
3. 重启应用程序

### 5. 训练过程问题

#### 错误：训练速度极慢
**可能原因**：
- 使用CPU训练
- 批次大小过小
- 数据加载慢

**解决方案**：
1. 使用GPU训练
2. 增加批次大小
3. 增加工作线程数
4. 使用SSD存储数据

#### 错误：损失不下降
**可能原因**：
- 学习率过高/过低
- 数据质量问题
- 模型选择不当

**解决方案**：
1. 调整学习率
2. 检查数据标注
3. 尝试不同模型

#### 错误：过拟合
**现象**：
- 训练损失下降，验证损失上升
- 验证指标不提升

**解决方案**：
1. 增加数据增强
2. 减少训练轮数
3. 使用更小的模型
4. 增加正则化

### 6. 系统兼容性问题

#### Windows特定问题
1. **路径问题**：使用反斜杠`\`
2. **权限问题**：以管理员身份运行
3. **编码问题**：确保使用UTF-8编码

#### macOS特定问题
1. **MPS支持**：确保PyTorch支持MPS
2. **权限问题**：检查文件夹权限

#### Linux特定问题
1. **显示问题**：确保X11转发正常
2. **依赖问题**：安装系统级依赖

## 🔧 高级故障排除

### 查看详细日志
1. 检查`yolo_trainer.log`文件
2. 在终端运行查看完整输出：
```bash
python run_yolo_trainer.py
```

### 环境重置
如果问题持续存在：
```bash
# 创建新的conda环境
conda create -n yolo11_new python=3.8
conda activate yolo11_new
pip install -r requirements.txt
```

### 手动测试组件
```bash
# 测试模型下载
python test_download.py

# 测试GUI组件
python test_gui.py

# 测试ultralytics
python -c "from ultralytics import YOLO; print('YOLO导入成功')"
```

## 📊 性能优化

### GPU优化
1. **检查GPU使用率**：
```bash
nvidia-smi
```

2. **优化GPU内存**：
- 使用混合精度训练（amp=True）
- 调整批次大小
- 清理GPU缓存

### CPU优化
1. **增加工作线程**：
- 设置workers为CPU核心数
- 不要超过CPU核心数

2. **内存优化**：
- 监控内存使用
- 关闭不必要程序

### 存储优化
1. **使用SSD**：提高数据加载速度
2. **数据预处理**：预先调整图像大小

## 🆘 获取帮助

### 自助诊断清单
- [ ] Python版本 >= 3.8
- [ ] 所有依赖包已安装
- [ ] 数据集格式正确
- [ ] 模型文件存在
- [ ] GPU驱动正常（如使用GPU）
- [ ] 足够的磁盘空间
- [ ] 网络连接正常

### 报告问题时请提供
1. **系统信息**：
   - 操作系统版本
   - Python版本
   - GPU型号（如有）

2. **错误信息**：
   - 完整的错误消息
   - yolo_trainer.log内容
   - 复现步骤

3. **配置信息**：
   - 使用的模型
   - 训练参数
   - 数据集大小

### 常用命令
```bash
# 检查Python版本
python --version

# 检查已安装包
pip list

# 检查GPU状态
nvidia-smi

# 检查磁盘空间
df -h  # Linux/macOS
dir    # Windows

# 测试网络连接
ping github.com
```

---

**记住**：大多数问题都有简单的解决方案，仔细阅读错误信息通常能找到答案！ 💪
