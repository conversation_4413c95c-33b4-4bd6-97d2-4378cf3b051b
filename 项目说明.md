# YOLO可视化训练界面 - 项目说明

## 项目概述

本项目是一个基于PySide6和ultralytics库开发的YOLO模型可视化训练界面，提供了完整的图形用户界面来训练YOLO模型，无需使用命令行。

## 主要特性

### ✨ 核心功能
- **🖥️ 可视化界面**: 直观的图形界面，支持所有YOLO训练参数配置
- **📥 自动模型下载**: 支持从官网自动下载YOLO11系列预训练模型
- **⚙️ 全参数配置**: 支持ultralytics库的所有训练参数自定义
- **📊 实时监控**: 实时显示训练进度、损失值和性能指标
- **💾 配置管理**: 支持训练配置的保存和加载
- **📝 日志记录**: 详细的训练日志记录和保存功能

### 🤖 支持的模型类型
- **目标检测**: yolo11n/s/m/l/x.pt
- **实例分割**: yolo11n/s/m/l/x-seg.pt  
- **姿态估计**: yolo11n/s/m/l/x-pose.pt
- **图像分类**: yolo11n/s/m/l/x-cls.pt
- **旋转目标检测**: yolo11n/s/m/l/x-obb.pt

## 文件结构

```
YOLO训练器/
├── yolo_trainer_gui.py      # 主界面文件
├── model_downloader.py      # 模型下载管理器
├── training_worker.py       # 训练工作线程
├── config_manager.py        # 配置管理器
├── utils.py                 # 工具函数
├── run_yolo_trainer.py      # 启动脚本
├── test_gui.py              # 测试脚本
├── requirements.txt         # 依赖包列表
├── README.md                # 详细说明文档
├── example_config.json      # 示例配置文件
├── install.bat              # Windows安装脚本
├── start.bat                # Windows启动脚本
├── configs/                 # 配置文件目录
│   └── example_dataset.yaml # 示例数据集配置
└── models/                  # 模型文件目录
```

## 快速开始

### 1. 环境要求
- Python 3.8+
- Windows 10/11, macOS, 或 Linux
- 建议使用conda环境

### 2. 安装依赖
```bash
# 方法1: 使用安装脚本（Windows）
install.bat

# 方法2: 手动安装
pip install -r requirements.txt
```

### 3. 启动应用
```bash
# 方法1: 使用启动脚本（Windows）
start.bat

# 方法2: 直接运行
python run_yolo_trainer.py

# 方法3: 测试模式
python test_gui.py
```

## 界面介绍

### 左侧配置面板
1. **模型配置标签页**
   - 预训练模型选择
   - 自动模型下载
   - 任务类型选择

2. **数据集配置标签页**
   - 数据集路径设置
   - 训练/验证/测试集配置
   - 类别数量和名称设置

3. **训练参数标签页**
   - 基础参数：轮数、批次大小、图像尺寸等
   - 优化器参数：学习率、动量、权重衰减等
   - 高级参数：混合精度、早停、保存周期等

4. **数据增强标签页**
   - 颜色增强：HSV调整
   - 几何变换：旋转、平移、缩放等
   - 混合增强：Mosaic、Mixup等

### 右侧监控面板
1. **训练状态**
   - 实时进度条
   - 当前轮次信息
   - 训练状态显示

2. **训练日志**
   - 实时日志输出
   - 日志保存功能
   - 自动滚动显示

## 使用流程

### 步骤1: 准备数据集
确保数据集采用YOLO格式：
```
dataset/
├── train/
│   ├── images/
│   └── labels/
├── val/
│   ├── images/
│   └── labels/
└── test/
    ├── images/
    └── labels/
```

### 步骤2: 配置模型
1. 选择预训练模型或上传自定义模型
2. 点击"下载预训练模型"自动下载
3. 选择对应的任务类型

### 步骤3: 配置数据集
1. 设置数据集根目录路径
2. 配置训练集、验证集路径
3. 设置类别数量和类别名称

### 步骤4: 调整参数
1. 根据需要调整训练参数
2. 配置数据增强选项
3. 可选择保存配置以便复用

### 步骤5: 开始训练
1. 点击"开始训练"按钮
2. 在右侧监控面板查看训练进度
3. 训练完成后查看结果

## 技术特点

### 🏗️ 架构设计
- **多线程架构**: 训练和下载在后台线程执行，界面响应流畅
- **信号槽机制**: 使用Qt信号槽实现组件间通信
- **模块化设计**: 各功能模块独立，便于维护和扩展

### 🔧 核心技术
- **PySide6**: 现代化的GUI框架
- **ultralytics**: 最新的YOLO训练库
- **多线程下载**: 支持断点续传和进度显示
- **配置管理**: JSON/YAML格式的配置文件

### 🛡️ 错误处理
- 完善的异常处理机制
- 详细的错误信息提示
- 自动日志记录和保存

## 高级功能

### 模型下载
- 支持25个预训练模型的自动下载
- 静默下载，通过浏览器或命令行工具
- 自动移动到项目models目录
- 下载进度实时显示

### 配置管理
- 支持完整训练配置的保存和加载
- JSON格式的配置文件
- 默认配置模板
- 配置验证功能

### 训练监控
- 实时显示训练进度和指标
- 详细的训练日志记录
- 支持训练过程中的停止操作
- 训练完成后的结果展示

## 故障排除

### 常见问题
1. **依赖包安装失败**: 使用conda环境或升级pip
2. **模型下载失败**: 检查网络连接或手动下载
3. **训练启动失败**: 检查数据集格式和路径
4. **GPU内存不足**: 减小批次大小或图像尺寸

### 日志文件
应用程序会生成`yolo_trainer.log`日志文件，包含详细的运行信息。

## 开发信息

### 开发环境
- Python 3.8.20
- PySide6 *******
- ultralytics 8.3.133
- PyTorch 2.4.1

### 测试状态
✅ 所有模块导入测试通过  
✅ GUI创建测试通过  
✅ 配置管理器测试通过  
✅ 模型下载器测试通过  

## 许可证

本项目基于MIT许可证开源。

## 更新日志

### v1.0.0 (2024-01-XX)
- 🎉 初始版本发布
- ✨ 完整的GUI界面
- 📥 自动模型下载功能
- 📊 实时训练监控
- ⚙️ 全参数配置支持
- 💾 配置保存/加载功能

---

**注意**: 这是一个功能完整的YOLO训练界面，适合研究人员、学生和开发者使用。如有问题或建议，欢迎反馈！
